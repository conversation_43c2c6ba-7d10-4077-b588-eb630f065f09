import { it, expect, describe } from 'vitest';
import dictionaryService from './dictionary';

describe('dictionaryService', () => {
  describe('findUsedWords', () => {
    it('empty dict, undefined', () => {
      expect(dictionaryService.findUsedWords('xin chào', null)).toEqual([]);
      expect(dictionaryService.findUsedWords('xin chào', undefined)).toEqual(
        [],
      );

      expect(dictionaryService.findUsedWords('xin chào', {})).toEqual([]);

      expect(
        dictionaryService.findUsedWords('xin chào', {
          invalidStructure: [1, 2, 3],
        }),
      ).toEqual([]);
    });

    it('valid dict', () => {
      const dict = {
        words: [
          {
            word: 'CNTT',
            pronunciation: 'công nghệ thông tin',
          },
          {
            word: 'AI',
            pronunciation: 'trí tuệ nhân tạo',
          },
        ],
      };

      expect(dictionaryService.findUsedWords('xin chào', dict)).toEqual([]);
      expect(dictionaryService.findUsedWords('xin chào AI', dict)).toEqual([
        {
          word: 'AI',
          pronunciation: 'trí tuệ nhân tạo',
        },
      ]);
    });
  });
});
