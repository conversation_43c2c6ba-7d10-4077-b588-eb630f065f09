const voiceDao = require('../daos/voice');
const voiceService = require('../services/voice');
const languageDao = require('../daos/language');
const { LANGUAGE_CODES } = require('../constants/voice');

const getVoices = async (req, res) => {
  const { publicIP: ip } = req.__clientInfo;
  const { search, fields, offset, limit, sort, searchFields } = req.query;

  // ADVISE: duplicated code with controllers/voiceCloning Duplicate code: lines 36
  const query = {};
  query.query = { active: true };
  query.useMicrosoftVoices = true;
  if (search) query.search = search;
  if (fields) query.fields = fields.split(',');
  if (offset) query.offset = parseInt(offset, 10);
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');
  if (searchFields) query.searchFields = searchFields.split(',');
  Object.keys(req.query)
    .filter(
      (q) =>
        ['search', 'fields', 'offset', 'limit', 'sort', 'searchFields'].indexOf(
          q,
        ) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { voices, total } = await voiceService.findVoices(query, ip);
  return res.send({ voices, metadata: { total } });
};

const getVoicesV2 = async (req, res) => {
  const { publicIP: ip } = req.__clientInfo;
  const { search, fields, offset, limit, sort, searchFields, voiceOwnership } =
    req.query;

  const { user = {} } = req || {};

  // ADVISE: duplicated code with controllers/voiceCloning Duplicate code: lines 36
  const query = {};
  query.query = { active: true };
  query.useMicrosoftVoices = true;
  if (search) query.search = search;
  if (fields) query.fields = fields.split(',');
  if (offset) query.offset = parseInt(offset, 10);
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');
  if (searchFields) query.searchFields = searchFields.split(',');
  if (voiceOwnership) query.voiceOwnership = voiceOwnership;
  Object.keys(req.query)
    .filter(
      (q) =>
        [
          'search',
          'fields',
          'offset',
          'limit',
          'sort',
          'searchFields',
          'voiceOwnership',
        ].indexOf(q) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { voices, total } = await voiceService.getVoicesV2(
    { user, ...query },
    ip,
  );
  return res.send({ voices, metadata: { total } });
};

const getLanguages = async (req, res) => {
  const { search, fields, offset, limit, sort, searchFields } = req.query;

  const query = {};
  query.query = {};
  // Not get multilingual language for not show this language in the list
  query.query = { ...query.query, code: { $ne: LANGUAGE_CODES.MULTILINGUAL } };
  // ADVISE: duplicated code with controllers/voiceCloning Duplicate code: lines 39
  if (search) query.search = search;
  if (fields) query.fields = fields.split(',');
  if (offset) query.offset = parseInt(offset, 10);
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');
  if (searchFields) query.searchFields = searchFields.split(',');
  Object.keys(req.query)
    .filter(
      (q) => ['search', 'fields', 'offset', 'limit', 'sort'].indexOf(q) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { languages, total } = await languageDao.findLanguages(query);
  return res.send({ languages, metadata: { total } });
};

const createVoices = async (req, res) => {
  const { voices } = req.body;
  const newVoices = await voiceDao.createVoices(voices);

  const { voices: allVoices } = await voiceDao.findVoices();
  global.VOICES = allVoices;

  return res.send({ voices: newVoices });
};

const updateVoice = async (req, res) => {
  const { voiceId } = req.params;
  const voice = await voiceDao.updateVoice(voiceId, req.body);

  const { voices } = await voiceDao.findVoices();
  global.VOICES = voices;

  return res.send({ voice });
};

const getVoicesV3 = async (req, res) => {
  const { user } = req || {};
  const { publicIP: ip } = req.__clientInfo;
  const DEFAULT_LIMIT = 30;
  const {
    search,
    fields,
    cursor,
    limit = DEFAULT_LIMIT,
    sort,
    searchFields,
    voiceOwnership,
  } = req.query;

  const query = {};
  query.query = { active: true };
  if (search) query.search = search;
  if (fields) query.fields = fields.split(',');
  if (cursor) query.cursor = cursor;
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');
  if (searchFields) query.searchFields = searchFields.split(',');
  if (voiceOwnership) query.voiceOwnership = voiceOwnership;

  Object.keys(req.query)
    .filter(
      (q) =>
        [
          'search',
          'fields',
          'cursor',
          'limit',
          'sort',
          'searchFields',
          'voiceOwnership',
        ].indexOf(q) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { voices, pagination } = await voiceService.getVoicesV3({
    query,
    user,
    ip,
  });

  return res.send({ voices, pagination });
};

module.exports = {
  getVoices,
  getLanguages,
  createVoices,
  updateVoice,
  getVoicesV2,
  getVoicesV3,
};
