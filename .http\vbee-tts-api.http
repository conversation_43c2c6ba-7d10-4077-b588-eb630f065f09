# ============================================================================
# these requests can be executed with humao.rest-client VSCode Extension
# ============================================================================

# 🏥 Health Check & System (6 endpoints)
# Startup, readiness, liveness probes
# Graceful shutdown, system globals

# 👤 User Management (5 endpoints)
# User info and management
# Credit locking
# Admin user queries

# 📚 Dictionary Management (2 endpoints)
# Personal dictionary CRUD

# ⏸️ Client Pause Settings (2 endpoints)
# Pause configuration management

# 🎵 Background Music (2 endpoints)
# Background music management


# 📄 Sample Scripts (1 endpoint)
# Get sample scripts

# ☁️ AWS Zone Management (1 endpoint)
# AWS zone configuration (Admin)

# 🐛 Error Reports (2 endpoints)
# Error report management (Admin)

# 🔧 TTS Admin (1 endpoint)
# Admin TTS request viewing

# 🔄 Migration (1 endpoint)
# Dubbing user migration


# ============================================================================
# BASE VARIABLES
# ============================================================================
@baseUrl = http://localhost:8000
# <EMAIL>

# ============================================================================
# VARIABLES FOR TESTING
# ============================================================================

@appId = f9258cf3-b0a8-4f7d-ad0e-0b343fb419f1
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NDc3MzI0Nzl9.gYCVKYQaMFxB4MLEN4ZDrkWIBHJYxWfE9gTSagoLH2cX
@requestId = sample-request-id-123
@voiceId = sample-voice-id-123
@userId = sample-user-id-456
@blackWordId = sample-blackword-id-789
@projectId = sample-project-id-101
@errorReportId = sample-error-report-id-202



# ============================================================================
# USER MANAGEMENT ENDPOINTS
# ============================================================================

### Get Current User TTS Info
GET {{baseUrl}}/api/v1/user-tts/{{{{userId}}}}
Authorization: Bearer {{token}}

### Get User by ID (Service-to-Service)
GET {{baseUrl}}/api/v1/user-tts/{{userId}}
Authorization: Bearer {{token}}

### Update User Lock Credits (Service-to-Service)
PUT {{baseUrl}}/api/v1/user-tts/{{userId}}/lock-credits
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "locked": true
}

### Get Users Not Reset Characters (Admin)
GET {{baseUrl}}/api/v1/users-not-reset-characters
Authorization: Bearer {{token}}

### Get Users Not Reset Seconds (Admin)
GET {{baseUrl}}/api/v1/users-not-reset-seconds
Authorization: Bearer {{token}}

# ============================================================================
# DICTIONARY MANAGEMENT ENDPOINTS
# ============================================================================

### Get User Dictionary
GET {{baseUrl}}/api/v1/dictionary
Authorization: Bearer {{token}}

### Create/Update Dictionary
POST {{baseUrl}}/api/v1/dictionary
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "words": [
        {
            "word": "VBEE",
            "pronunciation": "vi bi i"
        },
        {
            "word": "API",
            "pronunciation": "ây pi ai"
        }
    ]
}

# ============================================================================
# CLIENT PAUSE SETTINGS ENDPOINTS
# ============================================================================

### Get Client Pause Settings
GET {{baseUrl}}/api/v1/client-pause
Authorization: Bearer {{token}}

### Create/Update Client Pause Settings
POST {{baseUrl}}/api/v1/client-pause
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "paragraph_break": 1000,
    "sentence_break": 500,
    "major_break": 300,
    "medium_break": 200
}

# ============================================================================
# BACKGROUND MUSIC ENDPOINTS
# ============================================================================

### Get Background Musics
GET {{baseUrl}}/api/v1/background-musics
Authorization: Bearer {{token}}

### Create Background Music
POST {{baseUrl}}/api/v1/background-musics
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "Relaxing Music",
    "url": "https://example.com/music.mp3",
    "duration": 120
}




# ============================================================================
# SAMPLE SCRIPTS ENDPOINTS
# ============================================================================

### Get Sample Scripts
GET {{baseUrl}}/api/v1/sample-scripts

# ============================================================================
# AWS ZONE MANAGEMENT ENDPOINTS (Admin)
# ============================================================================

### Create AWS Zone (Admin)
POST {{baseUrl}}/api/v1/aws-zone
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "us-east-1",
    "region": "us-east-1",
    "weight": 100,
    "allow_request_types": ["API", "WEB"]
}

# ============================================================================
# ERROR REPORT ENDPOINTS (Admin)
# ============================================================================

### Get Error Reports (Admin)
GET {{baseUrl}}/api/v1/admin/error-reports?offset=0&limit=10
Authorization: Bearer {{token}}

### Get Specific Error Report (Admin)
GET {{baseUrl}}/api/v1/admin/error-reports/{{errorReportId}}
Authorization: Bearer {{token}}

# ============================================================================
# TTS ADMIN ENDPOINTS
# ============================================================================

### Get TTS Requests (Admin)
GET {{baseUrl}}/api/v1/admin/tts?offset=0&limit=10
Authorization: Bearer {{token}}

# ============================================================================
# MIGRATED DUBBING USER ENDPOINTS
# ============================================================================

### Agree to Migrate Dubbing User
PUT {{baseUrl}}/api/v1/migrated-dubbing-user
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "agreed": true
}

# ============================================================================
# SHORT URL ENDPOINTS
# Audio access via short URLs
# ============================================================================

### Access Audio via Short URL
GET {{baseUrl}}/s/{{requestId}}/{{token}}
