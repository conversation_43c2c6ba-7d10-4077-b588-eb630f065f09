const { DEFAULT_CLIENT_PAUSE } = require('../constants/clientPause');
const clientPauseDao = require('../daos/clientPause');
const { omitIsNil } = require('../utils/omit');

const createClientPause = async (
  userId,
  { paragraphBreak, sentenceBreak, majorBreak, mediumBreak },
) => {
  const clientPauseExist = await clientPauseDao.findClientPause(userId);

  const clientPause = omitIsNil(
    { paragraphBreak, sentenceBreak, majorBreak, mediumBreak },
    { deep: true },
  );

  if (clientPauseExist) {
    const { _id } = clientPauseExist;
    await clientPauseDao.updateClientPause(_id, clientPause);
  } else {
    await clientPauseDao.createClientPause({ userId, ...clientPause });
  }
};

const getClientPause = async (clientPause, userId) => {
  const clientPauseFromUser = await clientPauseDao.findClientPause(userId);

  const pauseConfig = {
    paragraphBreak: DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
    sentenceBreak: DEFAULT_CLIENT_PAUSE.SENTENCE_BREAK,
    majorBreak: DEFAULT_CLIENT_PAUSE.MAJOR_BREAK,
    mediumBreak: DEFAULT_CLIENT_PAUSE.MEDIUM_BREAK,
  };

  return {
    ...pauseConfig,
    ...clientPauseFromUser,
    ...clientPause,
  };
};

module.exports = { createClientPause, getClientPause };
