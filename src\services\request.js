const moment = require('moment');

const {
  REDIS_KEY_PREFIX,
  VN_DOMAIN,
  REGEX,
  REQUEST_TYPE,
  FREE_PACKAGE_CODES,
} = require('../constants');
const { STORAGE } = require('../constants/cloudStorage');
const { VOICE_STATUS } = require('../constants/voiceCloning');
const {
  DEFAULT_BUCKET_S3,
  DEFAULT_AWS_REGION,
  MAX_TITLE_LENGTH,
} = require('../configs');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');

const requestDao = require('../daos/request');
const voiceCloningDao = require('../daos/voiceCloning');
const { createInProgressRequest } = require('../daos/inProgressRequest');

const Caching = require('../caching');
const RequestCaching = require('../caching/requestCaching');

const appService = require('./app');
const { renameS3File, getKeyFromS3Url } = require('./s3');
const { getAccessToken } = require('./iam');
const { getAudioName, detectCloudStorageService } = require('./audio');
const { getAudioDownloadUrl } = require('./audioDownload');
const {
  addLanguageDetailToVoice,
  getVoiceWithCreditFactor,
} = require('./voice');
const { getPackageUsageOptions } = require('./package');
const { getKeyFromGCSUrl, renameGCSFile } = require('./googleCloudStorage');
const { AuthorizationService } = require('./authorization');
const { isVoiceCloningCode } = require('./voiceCloning');

const SPACE = ' ';
const CHINESE_CHARACTERS_LANGUAGE_CODES = ['cmn-CN', 'ja-JP', 'ko-KR'];

const checkMonetizable = ({ type, packageCode, voice, demo }) => {
  const { code: voiceCode, status } = voice || {};

  const isStudioRequest = type === REQUEST_TYPE.STUDIO;
  const isPaidPackage = !FREE_PACKAGE_CODES.includes(packageCode);
  const isVoiceCloning = isVoiceCloningCode(voiceCode);
  const isPublicVoice = status === VOICE_STATUS.PUBLIC;
  const isNotDemoRequest = !demo;

  const isMonetizable =
    isStudioRequest &&
    isPaidPackage &&
    isVoiceCloning &&
    isPublicVoice &&
    isNotDemoRequest;

  return isMonetizable;
};

/** persist TTSRequest into DB
 * @description side effect: modify the input argument, removed "requestId", add "_id"
 */
const createRequest = async (requestInfo) => {
  requestInfo = { ...requestInfo };
  requestInfo._id = requestInfo.requestId;
  delete requestInfo.requestId;

  const request = await requestDao.createRequest(requestInfo);

  const { _id, createdAt } = requestInfo;
  createInProgressRequest(_id, createdAt);

  return request;
};

const updateParagraphsOfRequest = async (requestId, paragraphs) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.BAD_REQUEST, 'Request is not found');

  const { paragraphs: paragraphsExists } = request;

  if (paragraphsExists === undefined) {
    await requestDao.updateRequestById(request._id, { paragraphs });
  }
};

const getApiRequest = async ({ requestId, appToken }) => {
  const selectFields = [
    'app',
    'characters',
    'voiceCode',
    'audioType',
    'speed',
    'bitrate',
    'progress',
    'createdAt',
    'status',
    'audioLink',
    'retentionPeriod',
  ];
  const request = await requestDao.findRequest(
    { _id: requestId },
    selectFields,
  );

  if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

  const {
    app: appId,
    characters,
    voiceCode,
    audioType,
    speed,
    bitrate,
    progress,
    createAt,
    status,
    retentionPeriod,
    createdAt,
  } = request;

  const app = await appService.getApp(appId, false);
  const { secretKey, token } = app;

  // ADVISE: BUSINESS: maybe relate to old API V3. why "no secretKey" is the problem ==> unauthorized?
  if (!secretKey) throw new CustomError(code.UNAUTHORIZED);
  if (appToken !== token) throw new CustomError(code.UNAUTHORIZED);

  const isAudioExpired = moment().isAfter(
    moment(createdAt).add(retentionPeriod, 'days'),
  );

  const result = {
    appId,
    requestId,
    characters,
    voiceCode,
    audioType,
    progress,
    speedRate: speed,
    bitrate,
    createAt,
    status,
  };

  if (isAudioExpired) result.audioExpired = true;
  else if (request.audioLink) {
    const accessToken = await getAccessToken();
    const audioLink = await getAudioDownloadUrl({
      requestId,
      authorization: `Bearer ${accessToken}`,
    });
    result.audioLink = audioLink;
  }

  return result;
};

const modifyFromS3Url = (originalUrl) => {
  const parts = originalUrl.split('/');
  const newUrl = `https://${VN_DOMAIN}/${parts[2].split('.')[0]}/${parts
    .slice(3)
    .join('/')}`;
  return newUrl;
};

const modifyFromGCSUrl = (originalUrl) => {
  const parsedUrl = new URL(originalUrl);
  parsedUrl.hostname = VN_DOMAIN;
  const newUrl = parsedUrl.toString();
  return newUrl;
};

const modifyVNUrl = (originalUrl) => {
  const cloudStorageFrom = detectCloudStorageService(originalUrl);

  switch (cloudStorageFrom) {
    case STORAGE.S3:
      return modifyFromS3Url(originalUrl);
    case STORAGE.GCS:
      return modifyFromGCSUrl(originalUrl);
    default:
      return originalUrl;
  }
};

const updateAudioLink = async (requestId, newTitle) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.BAD_REQUEST, 'Request is not found');

  const newAudioName = getAudioName(newTitle, requestId, request.userId);
  const {
    awsZoneFunctions = {},
    awsZoneSynthesis = DEFAULT_AWS_REGION,
    audioType,
    googleCloudStorage = {},
  } = request;
  const { s3Bucket = DEFAULT_BUCKET_S3 } = awsZoneFunctions;
  const { bucket: gcsBucket } = googleCloudStorage;

  const { audioLink } = request;
  const storage = detectCloudStorageService(audioLink);
  const isGCSStorage = storage === STORAGE.GCS;

  const oldAudioKey = isGCSStorage
    ? getKeyFromGCSUrl(audioLink)
    : getKeyFromS3Url(audioLink);

  const lastIndex = oldAudioKey.lastIndexOf('/');
  const directoryPath = oldAudioKey.substring(0, lastIndex + 1);

  const newKey = `${directoryPath}${newAudioName}.${audioType}`;

  const newAudioLink = isGCSStorage
    ? await renameGCSFile({
        bucketName: gcsBucket,
        oldKey: oldAudioKey,
        newKey,
      })
    : await renameS3File({
        bucketName: s3Bucket,
        oldKey: oldAudioKey,
        newKey,
        awsZoneSynthesis,
      });

  return newAudioLink;
};

const updateRequest = async (requestId, requestInfo) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.BAD_REQUEST, 'Request is not found');

  const { title } = requestInfo;
  const { audioLink } = request;
  if (audioLink) {
    const newAudioLink = await updateAudioLink(requestId, title);
    if (!newAudioLink)
      throw new CustomError(
        code.RENAME_TITLE_FAILURE,
        'Update audio link failed',
      );

    requestInfo.audioLink = newAudioLink;
  }

  const updatedRequest = await requestDao.updateRequestById(requestId, {
    ...requestInfo,
  });

  return updatedRequest;
};

const getPendingAndInprogressRequestKey = (userId, requestType) => {
  const baseKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS
      : REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS;

  return `${baseKey}_${userId}`;
};

const countPendingAndInProgressReq = async ({
  userId,
  requestType = REQUEST_TYPE.STUDIO,
}) => {
  const requestKey = getPendingAndInprogressRequestKey(userId, requestType);
  const numOfPendAndInprReq = await Caching.RedisRepo.get(requestKey);
  return numOfPendAndInprReq || 0;
};

const countPendingRequests = async ({ userId, requestType }) => {
  const user = await AuthorizationService.getUser(userId);

  const packageCode =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? user?.packageCode
      : user?.apiPackage?.packageCode;
  const packageUsageOptions = await getPackageUsageOptions({
    userId,
    packageCode,
    userUsageOptions: user,
  });
  const { concurrentRequest = 0 } = packageUsageOptions || {};

  const numberOfPendingAndInprReq = await countPendingAndInProgressReq({
    userId,
    requestType,
  });

  const totalPendingReq = Number(numberOfPendingAndInprReq);
  return { totalPendingReq, concurrentRequest };
};

const setPendingAndInProgressReqCount = async ({
  userId,
  requestType = REQUEST_TYPE.STUDIO,
  count = 0,
}) => {
  const requestKey = getPendingAndInprogressRequestKey(userId, requestType);
  await Caching.RedisRepo.set(requestKey, count);
};

const handleAudioUrl = async (requestId, token) => {
  const cacheKey = `${REDIS_KEY_PREFIX.AUDIO_URL}_${requestId}_${token}`;
  const audioUrl = await Caching.RedisRepo.get(cacheKey);
  if (!audioUrl) throw new CustomError(code.BAD_REQUEST);
  return audioUrl;
};

const getTitle = (text, voiceLanguage) => {
  const plainText = text
    .trim()
    .replace(REGEX.ADVANCE_TAG, '')
    .replace(REGEX.OLD_BREAK_TIME, '');

  if (plainText.length <= MAX_TITLE_LENGTH) {
    return plainText;
  }

  const lastSpaceIndex = plainText.lastIndexOf(SPACE, MAX_TITLE_LENGTH - 1);
  const firstSpaceIndex = plainText.indexOf(SPACE);

  if (
    firstSpaceIndex !== -1 &&
    firstSpaceIndex <= MAX_TITLE_LENGTH - 1 &&
    !CHINESE_CHARACTERS_LANGUAGE_CODES.includes(voiceLanguage)
  )
    return plainText.substring(0, lastSpaceIndex + 1);

  return plainText.substring(0, MAX_TITLE_LENGTH);
};

// Convert sentences voice code to string cause it's an object and when return to client, key will be camelCase
const stringifySentencesVoiceCode = (sentencesVoiceCode) =>
  JSON.stringify(sentencesVoiceCode);

const getRequest = async (requestId, userId) => {
  let request = await requestDao.findRequestById(requestId);
  const user = userId ? await AuthorizationService.getUser(userId, false) : {};
  const { packageCode } = user || {};

  request.voice = getVoiceWithCreditFactor(request.voice, packageCode); // TODO: Actually, this is wrong because credit factor can be changed depend on feature flag. Temporary return for frontend work.

  if (request?.sentences && request.sentences.length) {
    let sentences = await requestDao.getDetailSentencesByRequestId(requestId);
    sentences = sentences.map((sentence) => {
      let { voice } = sentence;
      voice = getVoiceWithCreditFactor(voice, packageCode);
      return { ...sentence, voice };
    });
    request = { ...request, sentences };
  }

  if (request?.sentencesVoiceCode)
    request.sentencesVoiceCode = stringifySentencesVoiceCode(
      request.sentencesVoiceCode,
    );

  return request;
};

const getRequests = async (query, userId) => {
  const { requests, total } = await requestDao.findRequests(query);
  // ADVISE: duplicated code, same file line 839
  const user = userId ? await AuthorizationService.getUser(userId, false) : {};
  const { packageCode } = user || {};

  const resultRequests = requests.map((request) => {
    if (request?.sentencesVoiceCode)
      request.sentencesVoiceCode = stringifySentencesVoiceCode(
        request.sentencesVoiceCode,
      );

    request.voice = getVoiceWithCreditFactor(request.voice, packageCode);
    return request;
  });

  return { requests: resultRequests, total };
};

const addVoiceDetailToSentence = async (sentence, packageCode) => {
  let voice = global.VOICES.find((v) => v.code === sentence.voiceCode);
  if (!voice) {
    const clonedVoice = await voiceCloningDao.findVoiceCloningByCode(
      sentence.voiceCode,
    );
    voice = clonedVoice;
  }

  voice = await addLanguageDetailToVoice(voice);
  voice = getVoiceWithCreditFactor(voice, packageCode);
  return { ...sentence, voice };
};

const getDetailSentencesForRequest = async (request, packageCode) => {
  const sentences = await Promise.all(
    request.sentences.map((sentence) =>
      addVoiceDetailToSentence(sentence, packageCode),
    ),
  );
  return sentences;
};

const getRequestV2 = async (requestId, userId) => {
  let request = await requestDao.findRequest({ _id: requestId });
  if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

  const user = userId ? await AuthorizationService.getUser(userId, false) : {};
  const { packageCode } = user || {};

  request = await requestDao.assignVoiceToRequest(request);
  let voice = await addLanguageDetailToVoice(request.voice);
  voice = getVoiceWithCreditFactor(voice, packageCode);
  request.voice = voice;
  delete request.voiceCode; // Remove voiceCode field cause voice field already has this info

  const hasSentences = request?.sentences && request.sentences.length;
  if (hasSentences) {
    // Add number of sentences to request cause get request v1 add this field
    request.numberOfSentences = request.sentences.length;
    const sentences = await getDetailSentencesForRequest(request, packageCode);
    request = { ...request, sentences };
  }

  if (request?.sentencesVoiceCode)
    request.sentencesVoiceCode = stringifySentencesVoiceCode(
      request.sentencesVoiceCode,
    );

  return request;
};

const getRequestsV2 = async (query, userId) => {
  const { requests, total } = await requestDao.findRequestsV2(query);
  const user = userId ? await AuthorizationService.getUser(userId, false) : {};
  const { packageCode } = user || {};

  const resultRequests = requests.map((request) => {
    if (request?.sentencesVoiceCode)
      request.sentencesVoiceCode = stringifySentencesVoiceCode(
        request.sentencesVoiceCode,
      );

    request.voice = getVoiceWithCreditFactor(request.voice, packageCode);
    return request;
  });

  return { requests: resultRequests, total };
};

const removePendingRequestByTypes = async ({ requestType, userId }) => {
  if (!Array.isArray(requestType) || requestType.length === 0) return;

  // ADVISE: performance: transform all into requestKey, then mdel in one operation
  await Promise.all(
    requestType.map((type) =>
      RequestCaching.removePending({ requestType: type, userId }),
    ),
  );
};

module.exports = {
  createRequest,

  updateParagraphsOfRequest,
  getApiRequest,
  modifyVNUrl,
  updateRequest,
  countPendingAndInProgressReq,
  countPendingRequests,
  setPendingAndInProgressReqCount,

  handleAudioUrl,
  getTitle,

  getRequests,
  getRequest,
  getRequestV2,
  getRequestsV2,
  checkMonetizable,

  removePendingRequestByTypes,
};
