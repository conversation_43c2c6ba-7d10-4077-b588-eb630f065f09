const KAFKA_TOPIC = {
  /** send */
  TTS_CREATED: 'tts-created',
  /** send, receive */
  TTS_SUCCESS: 'tts-success',
  /** send */
  TTS_FAILURE: 'tts-failure',
  /** send */
  TTS_IN_PROGRESS: 'tts-in-progress',
  /** receive */
  TTS_TIMEOUT: 'tts-timeout',

  /** send */
  RESET_FREE_CHARACTERS: 'reset-free-characters',

  /** send, receive */
  AI_VOICE_PUBLISH_TTS_SUCCESS: 'ai-voice-publish-tts-success',
  /** send, receive */
  AI_VOICE_PUBLISH_TTS_FAILURE: 'ai-voice-publish-tts-failure',

  /** receive */
  UPDATE_PROGRESS_REQUEST: 'update-progress-request',

  /** receive */
  PACKAGE_ACTIVATED: 'package-activated',
  CHARACTERS_PROCESSING: 'characters-processing',
  /** send */
  SYNC_CHARACTERS: 'sync-characters',

  /** send, receive */
  SECONDS_PROCESSING: 'seconds-processing',
  /** send */
  SYNC_SECONDS: 'sync-seconds',

  /** send, receive */
  STT_SECONDS_PROCESSING: 'stt-seconds-processing',

  /** send */
  ORDER_PAID: 'order-paid',

  /** received */
  MIGRATE_ACCOUNT: 'migrate-account',
  /** received */
  OVERWRITE_ACCOUNT: 'overwrite-account',
  /** received */
  SWITCH_VERSION: 'switch-version',
  /** received */
  SYNC_DICTIONARY: 'sync-dictionary',
  /** received */
  BLOCK_USER: 'block-user',

  /** send */
  API_CHARACTERS_RUN_OUT: 'api-characters-run-out',
  API_CHARACTERS_REACHED_ZERO: 'api-characters-reached-zero',

  /** receive */
  GET_LINK_SRT_FILE_SUCCESS: 'get-link-srt-file-success',
  /** send */
  GET_LINK_SRT_FILE_REQUEST: 'get-link-srt-file-request',
  /** receive */
  GET_LINK_SRT_FILE_FAILURE: 'get-link-srt-file-failure',

  /** send */
  DUBBING_FAILURE: 'dubbing-failure',

  /** receive */
  MIGRATE_DUBBING_TO_STUDIO: 'migrate-dubbing-to-studio',

  /** send */
  SYNC_REDEEM_REFERRAL: 'sync-redeem-referral',

  /** receive */
  COMMUNITY_VOICE_STATS: 'community-voice-stats',
};

const AUDIO_DOMAIN_TYPE = {
  VN: 'vn',
  S3: 's3',
};

/** PaygAPI */
const RESPONSE_TYPE = {
  /** sync wait, to return the audio file after TTS */
  DIRECT: 'direct',
  /** async, return ASAP, callback when the audio file ready */
  INDIRECT: 'indirect',
};

const API_RESPONSE_TYPE = {
  STUDIO_API: 'STUDIO_API',
  V3_ENTERPRISE_DIRECT: 'V3_ENTERPRISE_DIRECT',
  V3_ENTERPRISE_INDIRECT: 'V3_ENTERPRISE_INDIRECT',
  V3_PERSONAL: 'V3_PERSONAL',
  V3_ARTICLE: 'V3_ARTICLE',
};

const V3_RESPONSE_TYPE = {
  INDIRECT: 'indirect',
  DIRECT: 'direct',
};

const V3_API_TYPE = {
  ENTERPRISE: 'ENTERPRISE',
  PERSONAL: 'PERSONAL',
  ARTICLE: 'ARTICLE',
};

const OUTPUT_TYPE = {
  LINK: 'link',
  BINARY: 'binary',
};

const REQUEST_TYPE = {
  API: 'API',
  /** Some service like AICall (Huân) has special demand for synthesis. AICall use dedicated (different) flow for cost optimiziation, cost saving.
   * AICall does not use the standard synthesisService.handleRequest().
   * AICall only need to synthesis the phone number, customer name, ... the rest is template and does not change.
   * "xin chào CUSTOMER_NAME, có số điện thoại PHONE_NUMBER" ==> we only to convert CUSTOMER_NAME and PHONE_NUMBER to audio, then join with the template audio.
   */
  API_CACHING: 'API_CACHING',
  STUDIO: 'STUDIO',
  DUBBING: 'DUBBING',
  API_STT: 'API_STT',
};

const APP_ROLE = {
  ADMIN: 'ADMIN',
  DEVELOPER: 'DEVELOPER',
};

const VOICE_PROVIDER = {
  GOOGLE: 'google',
  AMAZON: 'amazon',
  VBEE: 'vbee',
  VBEE_VOICE_CLONING: 'vbee-voice-cloning',
  MICROSOFT: 'microsoft',
};

const NON_VBEE_VOICE_PROVIDERS = [
  VOICE_PROVIDER.GOOGLE,
  VOICE_PROVIDER.AMAZON,
  VOICE_PROVIDER.MICROSOFT,
];

const VOICE_LEVEL = {
  BASIC: 'BASIC',
  ADVANCED: 'ADVANCED',
  PREMIUM: 'PREMIUM',

  // TODO: Remove legacy voice levels after migration
  PRO: 'PRO',
  STANDARD: 'STANDARD',
};

const TTS_STATUS = {
  IN_PROGRESS: 'IN_PROGRESS',
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
};

const REQUEST_STATUS = {
  IN_PROGRESS: 'IN_PROGRESS',
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
};

const WS_MESSAGE_TYPE = {
  INIT: 'INIT',
  PING: 'PING',
  PONG: 'PONG',
  GET_REMAINING_PREVIEW: 'GET_REMAINING_PREVIEW',
  SYNTHESIS: 'SYNTHESIS',
  STREAM_REQUEST: 'STREAM_REQUEST',
};

const SYNTHESIS_TYPE = {
  SINGLE_VOICE: 'SINGLE_VOICE',
  MULTI_VOICE: 'MULTI_VOICE',
};

const FIVE_MINUTES = 5 * 60 * 1000;
const CHECKING_ALIVE_CONNECTION_INTERVAL = FIVE_MINUTES;

const SPEED_CONVERT = {
  FAST: 'FAST',
  LOW: 'LOW',
};

const SYNC_CHARACTERS_EVENT = {
  PAY_ORDER: 'PAY_ORDER',
  CANCEL_ORDER_AFTER_PAID: 'CANCEL_ORDER_AFTER_PAID',
  SPEND: 'SPEND',
  REFUND: 'REFUND',
  RESET_BONUS_CHARACTERS: 'RESET_BONUS_CHARACTERS',
  RESET_MONTHLY_CHARACTERS: 'RESET_MONTHLY_CHARACTERS',
  LOCK_CHARACTERS: 'LOCK_CHARACTERS',
  REFUND_PREVIEW_CHARACTERS: 'REFUND_PREVIEW_CHARACTERS',
  SWITCH_VERSION: 'SWITCH_VERSION',
  ADD_BONUS_CHARACTERS: 'ADD_BONUS_CHARACTERS',
  ADD_REMAINING_CHARACTERS: 'ADD_REMAINING_CHARACTERS',
  RESET_CREDITS_PER_CYCLE: 'RESET_CREDITS_PER_CYCLE',
  REDEEM_REFERRAL: 'REDEEM_REFERRAL',
};

const SYNC_SECONDS_EVENT = {
  PAY_ORDER: 'PAY_ORDER',
  CANCEL_ORDER_AFTER_PAID: 'CANCEL_ORDER_AFTER_PAID',
  SPEND: 'SPEND',
  REFUND: 'REFUND',
  LOCK_SECONDS: 'LOCK_SECONDS',
  RESET_BONUS_SECONDS: 'RESET_BONUS_SECONDS',
};

const SWITCH_VERSION_EVENT = {
  UPGRADE: 'UPGRADE',
};

const COMMUNITY_VOICE_STATS_EVENT = {
  DAILY_VOICE_STATS_COMPLETED: 'DAILY_VOICE_STATS_COMPLETED',
  SYNC_VOICE_STATS_TO_VOICE: 'SYNC_VOICE_STATS_TO_VOICE',
};

const LOADING_SYNTHESIS_FACTOR = {
  START_PROCESSING: 0.01,
  SENTENCE_TOKENIZATION: 0.1,
  SYNTHESIS_SENTENCE_SUCCESS: 0.8,
  JOIN_AUDIO: 0.1,
  MAX_PROCESSING: 0.99,
};

const PACKAGE_FEATURE = {
  /** users can use the tts (from studio) */
  TTS: 'tts',
  /** globalVoice means the user can use the global voice */
  GLOBAL_VOICE: 'global-voice',
  /** users can preview audio, include without paying and paying preview */
  PREVIEW: 'preview',
  /** users can use the client pause */
  CLIENT_PAUSE: 'client-pause',
  /** users can use the subtitles */
  SUBTITLE: 'subtitle',
  AI_VOICE_CLONING: 'ai-voice-cloning',
  AI_INSTANT_VOICE_CLONING: 'ai-instant-voice-cloning',
};

const FREE_PACKAGE_CODES = [
  'STUDIO-TRIAL',
  'STUDIO-BASIC',
  'STUDIO-FREE',
  'DUBBING-BASIC',
  'DUBBING-TRIAL',
  'STUDIO-FREE-V2',
  'STUDIO-FREE-V3',
];

const PACKAGE_CODE = {
  /** API Pay As You Go */
  API_PAYG: 'API-PAYG',
  API_ENTERPRISE_YEAR_V2: 'API-ENTERPRISE-YEAR-V2',
  STUDIO_TRIAL: 'STUDIO-TRIAL',
  STUDIO_FREE: 'STUDIO-FREE',
  STUDIO_FREE_V2: 'STUDIO-FREE-V2',
  STUDIO_FREE_V3: 'STUDIO-FREE-V3',
  STUDIO_BASIC: 'STUDIO-BASIC',
  DUBBING_TRIAL: 'DUBBING-TRIAL',
  DUBBING_BASIC: 'DUBBING-BASIC',
};

/** packages of Payg API buyer */
const SME_PACKAGE_CODES = [
  PACKAGE_CODE.API_PAYG,
  PACKAGE_CODE.API_ENTERPRISE_YEAR_V2,
];

const PACKAGE_TYPE = {
  STUDIO: 'STUDIO',
  API: 'API',
  ARTICLE: 'ARTICLE',
  CHARACTER: 'CHARACTER',
  DUBBING: 'DUBBING',
  MBF_DUBBING: 'MBF-DUBBING',
};

const PACKAGE_VERSION = {
  V1: 'V1',
  V2: 'V2',
  V2_1: 'V2.1',
  V3: 'V3',
};

const VIETNAMESE_LETTERS =
  'àáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ';

const VALID_LETTERS =
  'aAàÀảẢãÃáÁạẠăĂằẰẳẲẵẴắẮặẶâÂầẦẩẨẫẪấẤậẬbBcCdDđĐeEèÈẻẺẽẼéÉẹẸêÊềỀểỂễỄếẾệỆfFgGhHiIìÌỉỈĩĨíÍịỊjJkKlLmMnNoOòÒỏỎõÕóÓọỌôÔồỒổỔỗỖốỐộỘơƠờỜởỞỡỠớỚợỢpPqQrRsStTuUùÙủỦũŨúÚụỤưƯừỪửỬữỮứỨựỰvVwWxXyYỳỲỷỶỹỸýÝỵỴzZ';

const NUMBERS = '0123456789';

const BREAK_LINE = '\n';

const REGEX = {
  BREAK_LINE: /((\s*)(\n)(\s*))+/g,
  VBEE_SPEED: /(?:(\+|-)[0-9.]+%)/g, // ADVISE: regex is not good, "Single character alternation". Can be replaced with [+\-]
  OLD_BREAK_TIME: /[<]break\s+time[=]([0-9.]+)[s][/][>]/g, // ADVISE: regex is not good, '[<]' can be simplified to '<'
  NEW_BREAK_TIME: /[<]break\s+time[=]["]([0-9.]+)[s]["][/][>]/g, // ADVISE: regex is not good, '[<]' can be simplified to '<', and similar single character capture
  // ADVISE: regex is not good, "Single character alternation". Can be replaced with [+\-]
  // ADVISE: regex is not good, '[<]' can be simplified to '<', and similar single character capture
  /** SSML tag. When we are in TTS_CORE_VERSION.NEW */
  ADVANCE_TAG:
    /[<]break\s+time[=]["]([0-9.]+)[s]["][/][>]|[<]emphasis\s+level[=]["](?:strong|moderate|reduced)["][>]|[<][/]emphasis[>]|[<]prosody\s+rate[=]["](?:x-fast|fast|medium|slow|x-slow|(?:(\+|-)[0-9.]+%))["][>]|[<][/]prosody[>]/g,
  CACHING_PERSONAL_TAG: /{.+?}/g,
  TEXT_AND_NUMBER: /[^a-zA-Z0-9 ]/g,
  SPACE: /\s+/g,
};

// ADVISE: This is not readable code. Please use a more descriptive name than OLD NEW (eg: V1_0, V1_1_SUPPORT_XYZ)
const TTS_CORE_VERSION = {
  /** 1.0 */
  OLD: '1.0',
  /** 1.1 change in break time tag */
  NEW: '1.1',
};

const TTS_CORE_COMPUTE_PLATFORM = {
  LAMBDA: 'LAMBDA',
  GPU: 'GPU',
  CLOUD_RUN: 'CLOUD_RUN',
};

const EMPHASIS_LEVEL = {
  STRONG: 'strong',
  MODERATE: 'moderate',
  REDUCED: 'reduced',
};

const VALID_SPEED = {
  MIN: 0.25,
  MAX: 1.9,
};

const VALID_SAMPLE_RATE = [8000, 16000, 22050, 32000, 44100, 48000];

const VALID_BIT_RATE = [8, 16, 32, 64, 128, 160];

const VALID_BACKGROUND_MUSIC_VOLUME = {
  MIN: 0,
  MAX: 100,
  DEFAULT: 80,
};

const VALID_CHARACTERS_LENGTH_REGEX = new RegExp(
  `([a-zA-Z${VIETNAMESE_LETTERS}]|[0-9]){30,}`,
  'gi',
);

const VALID_CHARACTERS_REGEX = new RegExp(`[${VALID_LETTERS}${NUMBERS}]`);

const TIME_DELTA_REGEX =
  /^(?:[0-9]{2}:[0-5][0-9]:[0-5][0-9](?:[.,][0-9]{1,3})?)$/; // Eg: 00:59:59,000 or 00:59:59

const REDIS_KEY_PREFIX = {
  STUDIO_PENDING_REQUESTS: 'AI_VOICE_STUDIO_PENDING_REQUESTS', // pending request ids of user studio
  API_PENDING_REQUESTS: 'AI_VOICE_API_PENDING_REQUESTS', // pending request ids of user api
  STUDIO_PENDING_AND_INPROGRESS_REQUESTS:
    'AI_VOICE_STUDIO_PENDING_AND_INPROGRESS_REQUESTS', // number of pending and inprogress requests of user studio
  API_PENDING_AND_INPROGRESS_REQUESTS:
    'AI_VOICE_API_PENDING_AND_INPROGRESS_REQUESTS', // number of pending and inprogress requests of user api
  STUDIO_PENDING_AND_INPROGRESS_TTS:
    'AI_VOICE_STUDIO_PENDING_AND_INPROGRESS_TTS', // number of pending and inprogress sentence tts of user studio
  API_PENDING_AND_INPROGRESS_TTS: 'AI_VOICE_API_PENDING_AND_INPROGRESS_TTS', // number of pending and inprogress sentence stt of user api
  API_STT_PENDING_AND_INPROGRESS_REQUESTS:
    'AI_VOICE_API_STT_PENDING_AND_INPROGRESS_REQUESTS', // number of pending and inprogress requests of user api
  FAILURE_REQUEST: 'AI_VOICE_FAILURE_REQUEST', // number of failure of request
  SUCCESS_REQUEST: 'AI_VOICE_SUCCESS_REQUEST', // number of success of request

  DEMO_TTS: 'AI_VOICE_DEMO_TTS', // number of demo tts on landing page
  PREVIEW_TTS: 'AI_VOICE_PREVIEW_TTS',
  PREVIEW_TTS_RATE_LIMIT: 'AI_VOICE_PREVIEW_TTS_RATE_LIMIT', // rate limit of preview tts for paid users
  TTS_RATE_LIMIT: 'AI_VOICE_TTS_RATE_LIMIT', // rate limit of tts for free users
  REQUEST: 'AI_VOICE_REQUEST', // prefix of cache request
  TTS: 'AI_VOICE_TTS', // prefix of cache tts
  SENTENCE: 'AI_VOICE_SENTENCE', // prefix of cache sentence

  SENTENCE_TOKENIZATION_STATUS: 'AI_VOICE_SENTENCE_TOKENIZATION_STATUS', // prefix of cache sentence tokenization status
  MIGRATED_TTS: 'AI_VOICE_MIGRATED_TTS', // prefix of check migrated tts
  APP: 'AI_VOICE_APP', // prefix of app

  TTS_PROCESSING_TIME: 'AI_VOICE_TTS_PROCESSING_TIME', // prefix of tts processing time
  TOTAL_TTS: 'AI_VOICE_TOTAL_TTS', // total tts counter

  SAVE_PROCESSING_TIME: 'AI_VOICE_SAVE_PROCESSING_TIME', // check saved processing time
  AUDIO_URL: 'AUDIO_URL',
  USAGE_OPTIONS: 'USAGE_OPTIONS',

  VC_SAMPLE_AUDIO: 'VC_SAMPLE_AUDIO',
  VC_SAMPLE_AUDIOS_QUEUE: 'VC_SAMPLE_AUDIOS_QUEUE',

  COMMUNITY_VOICE_STATS: 'COMMUNITY_VOICE_STATS',
};

const REDIS_KEY_TTL = {
  FAILURE_REQUEST: 60 * 60 * 6, // 6 hours
  SUCCESS_REQUEST: 60 * 60 * 6, // 6 hours

  SYNTHESIS_REQUEST: 60 * 60 * 6, // 6 hours (for cache synthesis request)
  SYNTHESIS_TTS: 60 * 60 * 6, // 6 hours (for cache synthesis tts)

  SENTENCE: 60 * 60 * 6, // 6 hours (for cache sentence)
  SENTENCE_TOKENIZATION_STATUS: 60 * 60 * 6, // 6 hours (for cache sentence tokenizer status)
  MIGRATED_TTS: 60 * 60 * 6, // 6 hours (for cache sentence tokenizer status)
  CACHE_AUDIO: 60 * 60 * 6, // 6 hours

  TTS_PROCESSING_TIME: 60 * 60 * 6, // 6 hours
  USAGE_OPTIONS: 60 * 30, // 30 minutes

  VC_SAMPLE_VOICE: 60 * 60 * 6, // 6 hours
  COMMUNITY_VOICE_STATS: 60 * 60 * 24, // 24 hours
};

const API_V3_URL = {
  API: '/api/old/tts',
  GET_REQUEST: '/api/old/requests',
};

const VN_DOMAIN = 'hn.vbee.vn';

const SERVICE_TYPE = {
  AI_VOICE: 'AI_VOICE',
};

const TTS_PROCESSING_STEPS = {
  PRE_PROCESSING: 'preProcessing',
  QUEUE: 'queue',
  SENTENCE_TOKENIZER: 'sentenceTokenizer',
  SYNTHESIS: 'synthesis',
  AUDIO_JOINER: 'audioJoiner',
};

const START_STEP_NAME = {
  [TTS_PROCESSING_STEPS.PRE_PROCESSING]: 'startTime',
  [TTS_PROCESSING_STEPS.QUEUE]: 'pushToQueueAt',
  [TTS_PROCESSING_STEPS.SENTENCE_TOKENIZER]: 'startSentenceTokenizerAt',
  [TTS_PROCESSING_STEPS.SYNTHESIS]: 'startSynthesisAt',
  [TTS_PROCESSING_STEPS.AUDIO_JOINER]: 'startJoinAudiosAt',
};

const ESTIMATED_TIME_PER_ONE_THOUSAND_CHARACTERS = 10; // 10s

const MAX_TITLE_CHARACTERS = 50;

const AUDIO_URL_TYPE = {
  ORIGINAL_URL: 'original-url',
  PRESIGNED_URL: 'presigned-url',
  SHORT_URL: 'short-url',
};

const MOE_EVENT = {
  MAKE_REQUEST: 'make_request',
};

const DATASENSES_EVENTS = {
  UPDATE_PREVIEW_STATUS: 'update_preview_status',
  UPDATE_MAKE_REQUEST: 'update_request_status',
  DOWNLOAD: 'download',
};

const DATASENSES_CLIENT_ID_DEFAULT = '00000000-0000-0000-0000-000000000000';

const API_VERSION = {
  V1: 'v1',
  V2: 'v2',
};

const RESOURCE_TYPE = {
  SECONDS: 'seconds',
  CHARACTERS: 'characters',
};

const DEFAULT_BITRATE = 128;

const RESET_CREDITS_STATUS = {
  IN_PROGRESS: 'IN_PROGRESS',
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
};

const LOCK_ACTIONS = { LOCK: 'LOCK', UNLOCK: 'UNLOCK' };

const WALLET_TYPES = {
  STUDIO_CYCLE: 'studio_cycle',
  STUDIO_TOP_UP: 'studio_top_up',
  STUDIO_CUSTOM: 'studio_custom',
  STUDIO_ONE_TIME: 'studio_one_time',
};

const PRODUCT_TYPE = {
  DUBBING: 'DUBBING',
  TTS: 'TTS',
};

const BLOCK_SYNTHESIS_STATUS = {
  IN_PROGRESS: 'IN_PROGRESS',
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
};

const AUDIO_MODE = {
  JOINED: 'joined',
  SEPARATE: 'separate',
};

const VOICE_CLONING_TYPE = {
  ZERO_SHOT: 'zero-shot',
  FINETUNE_SCRIPTS_RECORDING: 'finetune-scripts-recording', // default
  FINETUNE_SCRIPTS_UPLOADING: 'finetune-scripts-uploading',
  FINETUNE_LONG_AUDIO: 'finetune-long-audio',
};

const VOICE_TYPE = {
  TTS: 'tts',
  CLONING: 'voice-cloning',
  ZERO_SHOT: 'zero-shot',
};

const DIRECTION = {
  NEXT: 'next',
  PREV: 'prev',
};

module.exports = {
  KAFKA_TOPIC,
  VOICE_PROVIDER,
  VOICE_LEVEL,
  TTS_STATUS,
  REQUEST_STATUS,
  WS_MESSAGE_TYPE,
  CHECKING_ALIVE_CONNECTION_INTERVAL,
  SYNTHESIS_TYPE,
  SPEED_CONVERT,
  SYNC_CHARACTERS_EVENT,
  SYNC_SECONDS_EVENT,
  LOADING_SYNTHESIS_FACTOR,
  PACKAGE_FEATURE,
  PACKAGE_CODE,
  FREE_PACKAGE_CODES,
  SME_PACKAGE_CODES,
  PACKAGE_TYPE,
  PACKAGE_VERSION,
  VIETNAMESE_LETTERS,
  BREAK_LINE,
  SWITCH_VERSION_EVENT,
  REGEX,
  TTS_CORE_VERSION,
  TTS_CORE_COMPUTE_PLATFORM,
  EMPHASIS_LEVEL,
  AUDIO_DOMAIN_TYPE,
  RESPONSE_TYPE,
  API_RESPONSE_TYPE,
  V3_RESPONSE_TYPE,
  V3_API_TYPE,
  OUTPUT_TYPE,
  REQUEST_TYPE,
  APP_ROLE,
  VALID_SPEED,
  VALID_SAMPLE_RATE,
  VALID_BIT_RATE,
  VALID_BACKGROUND_MUSIC_VOLUME,
  VALID_CHARACTERS_LENGTH_REGEX,
  VALID_CHARACTERS_REGEX,
  VALID_LETTERS,
  TIME_DELTA_REGEX,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  API_V3_URL,
  VN_DOMAIN,
  SERVICE_TYPE,
  TTS_PROCESSING_STEPS,
  START_STEP_NAME,
  ESTIMATED_TIME_PER_ONE_THOUSAND_CHARACTERS,
  AUDIO_URL_TYPE,
  MAX_TITLE_CHARACTERS,
  MOE_EVENT,
  DATASENSES_EVENTS,
  DATASENSES_CLIENT_ID_DEFAULT,
  API_VERSION,
  DEFAULT_BITRATE,
  NON_VBEE_VOICE_PROVIDERS,
  RESOURCE_TYPE,
  RESET_CREDITS_STATUS,
  LOCK_ACTIONS,
  WALLET_TYPES,
  PRODUCT_TYPE,
  BLOCK_SYNTHESIS_STATUS,
  AUDIO_MODE,
  VOICE_CLONING_TYPE,
  VOICE_TYPE,
  DIRECTION,
  COMMUNITY_VOICE_STATS_EVENT,
};
