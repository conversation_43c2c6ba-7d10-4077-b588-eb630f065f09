const { TTS_API_URL } = require('../configs');
const { TTS_CALLBACK_ACTION } = require('../constants/tts');
const { MAX_TTS_PRESET } = require('../constants/ttsPreset');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const callApi = require('../utils/callApi');
const logger = require('../utils/logger');

const ttsPresetDao = require('../daos/ttsPreset');
const voiceCloningDao = require('../daos/voiceCloning');

const { TtsGateAdapter } = require('../adapters/ttsgate');

const getTts = async ({
  search,
  searchFields,
  dateField,
  query,
  offset,
  limit,
  fields,
  sort,
  requestId,
}) => {
  const ttsRes = await TtsGateAdapter.getTts({
    search,
    searchFields,
    dateField,
    query,
    offset,
    limit,
    fields,
    sort,
    requestId,
  });
  const res = ttsRes.result || {};

  const { tts, total } = res;
  if (!tts?.length) return { tts: [], total: 0 };

  return { tts, total };
};

const assignVoiceToTtsPreset = async (ttsPreset) => {
  const voiceInGlobal = global.VOICES.find(
    (voice) => voice.code === ttsPreset.voiceCode,
  );
  if (voiceInGlobal) return { ...ttsPreset, voice: voiceInGlobal };

  const clonedVoice = await voiceCloningDao.findVoiceCloningByCode(
    ttsPreset.voiceCode,
  );
  return { ...ttsPreset, voice: clonedVoice };
};

const validateTtsPresetName = async ({ userId, name, excludeId }) => {
  const existingTtsPreset = await ttsPresetDao.getTtsPresetByName({
    userId,
    name,
    excludeId,
  });
  if (existingTtsPreset)
    throw new CustomError(
      errorCodes.TTS_PRESET_NAME_ALREADY_EXISTS,
      'TTS preset name already exists',
    );
};

const getTtsPresets = async (userId) => {
  const { ttsPresets, total } = await ttsPresetDao.getTtsPresets(userId);
  const ttsPresetsWithVoice = await Promise.all(
    ttsPresets.map(assignVoiceToTtsPreset),
  );
  return { ttsPresets: ttsPresetsWithVoice, total };
};

const createTtsPreset = async (
  userId,
  { name, audioType, backgroundMusic, speed, voiceCode, clientPause },
) => {
  const createFields = {
    userId,
    name,
    audioType,
    backgroundMusic,
    speed,
    voiceCode,
    clientPause,
  };
  const count = await ttsPresetDao.countTtsPresetByUserId(userId);
  if (count >= MAX_TTS_PRESET)
    throw new CustomError(
      errorCodes.TTS_PRESET_LIMIT_REACHED,
      'TTS preset limit reached',
    );

  await validateTtsPresetName({ userId, name });

  const ttsPreset = await ttsPresetDao.createTtsPreset(createFields);
  return ttsPreset;
};

const getTtsPresetById = async (userId, ttsPresetId) => {
  const ttsPreset = await ttsPresetDao.getTtsPresetById(userId, ttsPresetId);
  const ttsPresetWithVoice = await assignVoiceToTtsPreset(ttsPreset);
  return ttsPresetWithVoice;
};

const updateTtsPreset = async (userId, ttsPresetId, updateFields) => {
  const { name } = updateFields;

  await validateTtsPresetName({ userId, name, excludeId: ttsPresetId });

  const ttsPreset = await ttsPresetDao.updateTtsPreset({
    userId,
    ttsPresetId,
    updateFields,
  });
  return ttsPreset;
};

const deleteTtsPreset = async (userId, ttsPresetId) => {
  await ttsPresetDao.deleteTtsPreset(userId, ttsPresetId);
};

const callApiSynthesis = async ({
  inputText,
  speedRate,
  voiceCode,
  bitrate,
  sampleRate,
  audioType,
  callbackUrl,
  returnTimestampWords = false,
  appToken,
  appId,
}) => {
  // ADVISE: self-call to this service via HTTP?
  const response = await callApi({
    method: 'POST',
    url: `${TTS_API_URL}/api/v1/tts`,
    data: {
      voiceCode,
      speedRate,
      inputText,
      app_id: appId,
      bitrate,
      sampleRate,
      audioType,
      responseType: 'indirect',
      returnTimestampWords,
      callbackUrl,
    },
    headers: {
      Authorization: `Bearer ${appToken}`,
    },
    timeout: 120000,
  });
  return response;
};

const handleCallbackTTS = async ({
  requestId,
  status,
  audioLink,
  audioType,
  action,
}) => {
  logger.info('Handle callback TTS', {
    requestId,
    status,
    audioLink,
    audioType,
    action,
    ctx: 'handleCallbackTTS',
  });

  switch (action) {
    case TTS_CALLBACK_ACTION.UPDATE_VC_SAMPLE_AUDIOS:
      await require('./voiceCloning').updateSampleAudioLink({
        status,
        requestId,
        audioLink,
        audioType,
      });
      break;

    case TTS_CALLBACK_ACTION.PROCESS_NEXT_SAMPLE_AUDIO:
      await require('./voiceCloning').processNextSampleAudio({
        requestId,
        status,
        audioLink,
      });
      break;

    default:
      throw new CustomError(
        errorCodes.INVALID_TTS_CALLBACK_ACTION,
        'Invalid TTS callback action',
      );
  }
};

module.exports = {
  getTts,
  getTtsPresets,
  getTtsPresetById,
  createTtsPreset,
  updateTtsPreset,
  deleteTtsPreset,
  callApiSynthesis,
  handleCallbackTTS,
};
