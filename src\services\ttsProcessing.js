const moment = require('moment');
const { AI_VOICE_APP_ID, AI_VOICE_CALLBACK_URL } = require('../configs');
const {
  WS_MESSAGE_TYPE,
  KAFKA_TOPIC,
  LOADING_SYNTHESIS_FACTOR,
  REQUEST_TYPE,
  RESPONSE_TYPE,
  REQUEST_STATUS,
  SME_PACKAGE_CODES,
  PACKAGE_FEATURE,
  BLOCK_SYNTHESIS_STATUS,
  TTS_PROCESSING_STEPS,
  AUDIO_DOMAIN_TYPE,
} = require('../constants');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const code = require('../errors/code');

const logger = require('../utils/logger');
const { isEmptyObject } = require('../utils/object');

const { TtsGateAdapter } = require('../adapters/ttsgate');
const DubbingAdapter = require('../adapters/dubbingAdapter');
const { sendMessage } = require('./kafka/producer');
const { getFeatureValue } = require('./growthbook');

const RequestCaching = require('../caching/requestCaching');
const ProcessingTimeCaching = require('../caching/processingTimeCaching');

const { deleteInProgressRequest } = require('../daos/inProgressRequest');
const {
  updateFinalRequestFromRedisToDB,
  findRequestById,
  updateRequestById,
} = require('../daos/request');
const requestDao = require('../daos/request');
const { updateBlockAudioLink } = require('../daos/project');
const { migrateTtsFromRedisToDB } = require('../daos/tts');
const { findUser } = require('../daos/user');
const { findDictionary } = require('../daos/dictionary');
const { findClientPause } = require('../daos/clientPause');

const WebSocketServer = require('./ws');
const RequestService = require('./request');
const { sendApiResponse } = require('./apiResponse');
const { refundCharacters } = require('./characterProcessing');
const {
  processQueueWhenRequestSuccess,
  processQueueWhenRequestFailure,
} = require('./queue'); // ADVISE: Circular deps
const {
  saveProcessingTime,
  getCurrentStep,
  getStartTimeFromStep,
} = require('./processingTime');
const { refundSeconds } = require('./secondProcessing');
const { getPackageUsageOptions } = require('./package');
const { getSynthesisComputePlatform } = require('./synthesisComputePlatform');
const { getVoiceCloningVoice } = require('./voiceCloning');
const AudioService = require('./audio');

// ADVISE: trigger by unused Kafka JOIN_AUDIOS_FAILURE, should be delete? because it exists in tts-api
const handleJoinAudiosFailureResponse = async ({
  requestId,
  ttsRequestId,
  error,
  errorCode,
}) => {
  const request = await RequestCaching.findRequestByIdInRedis(requestId);
  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  const processingTime = await ProcessingTimeCaching.getProcessingTime(
    requestId,
  );
  const { startJoinAudiosAt, startTime } = processingTime;
  await ProcessingTimeCaching.saveStepProcessingTime({
    requestId,
    step: TTS_PROCESSING_STEPS.AUDIO_JOINER,
    startTime: startJoinAudiosAt,
    additionalFields: undefined,
  });
  saveProcessingTime({ requestId, startTime, status: REQUEST_STATUS.FAILURE });

  const endedAt = new Date();
  const finalRequest = await RequestCaching.updateRequestByIdInRedis(
    requestId,
    {
      status: REQUEST_STATUS.FAILURE,
      ttsRequestId,
      error,
      endedAt,
      errorCode,
    },
  );

  updateFinalRequestFromRedisToDB(requestId, finalRequest);
  deleteInProgressRequest(requestId);
  migrateTtsFromRedisToDB(requestId);

  // sendRequestToDataSenses(request);

  handleTtsFailure({
    request: finalRequest,
    errorCode: code.TTS_FAILURE,
    errorMessage: error,
  });
};

// ADVISE: was triggered by unused Kafka JOIN_AUDIOS_SUCCESS, should be delete? because it exists in tts-api
const handleJoinAudiosSuccessResponse = async ({
  requestId,
  audioLink: originalAudioLink,
  duration,
  ttsRequestId,
  timestampWords,
}) => {
  const request = await requestDao.findRequestById(requestId);
  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  // Copy audio to another bucket for saving long time
  let audioLink = originalAudioLink;
  if (!request.audioLink)
    audioLink = await AudioService.copyCloudFile(originalAudioLink, request);

  await RequestCaching.updateRequestByIdInRedis(requestId, {
    progress: 100,
    joinAudioDuration: duration,
    ttsRequestId,
    timestampWords,
  });
  await deleteInProgressRequest(requestId);
  const { audioDomainType } = request;
  const finalAudioLink =
    audioDomainType === AUDIO_DOMAIN_TYPE.VN
      ? RequestService.modifyVNUrl(audioLink)
      : audioLink;
  const finalRequest = await RequestCaching.saveAudioLinkInRedis(
    requestId,
    finalAudioLink,
  );
  const formattedRequest = {
    ...finalRequest,
    sourceAudioLink: originalAudioLink,
  };
  await requestDao.updateFinalRequestFromRedisToDB(requestId, formattedRequest);

  const processingTime = await ProcessingTimeCaching.getProcessingTime(
    requestId,
  );
  const { startJoinAudiosAt, startTime } = processingTime;
  await ProcessingTimeCaching.saveStepProcessingTime({
    requestId,
    step: TTS_PROCESSING_STEPS.AUDIO_JOINER,
    startTime: startJoinAudiosAt,
    additionalFields: undefined,
  });
  saveProcessingTime({ requestId, startTime, status: REQUEST_STATUS.SUCCESS });

  // sendRequestToDataSenses({ ...request, status: REQUEST_STATUS.SUCCESS });

  handleTtsSuccess(finalRequest);
};

const handleTtsDemoResponse = ({ requestId, status, audioLink }) => {
  const ws = global.REQUESTS[requestId];
  if (!ws) return;

  const message = {
    type: WS_MESSAGE_TYPE.SYNTHESIS,
    status: 1,
    result: { requestId, status, audioLink },
  };
  WebSocketServer.sendMessage(ws, message);

  // if (status === REQUEST_STATUS.SUCCESS) decreaseMaxPreviewByRequest(requestId);
};

const handleStreamAudio = async ({
  requestId,
  index,
  subIndex,
  audioLink,
  phrases,
  tts,
}) => {
  const ws = global.REQUESTS[requestId];
  if (!ws) return;

  const request = await RequestCaching.findRequestByIdInRedis(requestId);

  logger.info(
    { requestId, index, subIndex, audioLink, audioIndexOrder: tts, phrases },
    { ctx: 'StreamAudio' },
  );

  WebSocketServer.sendMessage(ws, {
    type: WS_MESSAGE_TYPE.STREAM_REQUEST,
    status: 1,
    result: {
      status: REQUEST_STATUS.SUCCESS,
      requestId,
      index,
      subIndex,
      audioLink,
      tts,
      phrases,
    },
  });

  if (index === 0 && subIndex === 0) {
    const firstStreamAudioAt = moment();
    const firstStreamAudioDuration = moment
      .duration(firstStreamAudioAt.diff(request.createdAt))
      .asSeconds();
    await RequestCaching.updateRequestByIdInRedis(requestId, {
      firstStreamAudioDuration,
    });
    logger.info('handleStreamAudio', {
      ctx: 'StreamAudio',
      firstStreamAudioDuration,
    });
  }
};

const refundResources = ({ userId, appId, requestId, seconds }) => {
  if (seconds) refundSeconds({ userId, appId, requestId });
  else refundCharacters({ userId, appId, requestId });
};

const handleRefundResources = ({
  requestId,
  type,
  userId,
  packageCode,
  appId,
  seconds,
}) => {
  const DUBBING_PACKAGE_PREFIX = 'DUBBING';
  const isProcessBySecond = packageCode.includes(DUBBING_PACKAGE_PREFIX);

  if (type === REQUEST_TYPE.DUBBING && isProcessBySecond)
    refundResources({ requestId, userId, appId, seconds });
  else refundResources({ requestId, userId, appId });
};

const handleTtsFailure = ({ request, errorCode, errorMessage }) => {
  const {
    _id: requestId,
    userId,
    app: appId,
    usedCharacters = {},
    type,
    responseType,
    demo,
    packageCode,
    seconds,
    projectId,
  } = request;

  // Refund and send to notification
  if (!demo) {
    processQueueWhenRequestFailure({
      userId,
      requestId,
      requestType: type,
    });
    if (!SME_PACKAGE_CODES.includes(packageCode))
      handleRefundResources({
        requestId,
        type,
        packageCode,
        userId,
        appId,
        seconds,
        usedCharacters,
      });
    sendMessage(KAFKA_TOPIC.TTS_FAILURE, {
      value: {
        projectId,
        requestId,
        appId,
        userId,
        paidCharacters: usedCharacters.paid || 0,
        bonusCharacters: usedCharacters.bonus || 0,
      },
    });
  }

  const { title, text, sentences, paragraphs, ...requestData } = request;

  // Send callback for sync request api
  if (type === REQUEST_TYPE.API && responseType === RESPONSE_TYPE.INDIRECT) {
    sendApiResponse({
      request: { requestId, ...requestData },
      errorCode,
      errorMessage,
    }).catch((e) => {
      logger.error(e, { ctx: 'SendApiResponseTtsFailure', requestId });
    });
  }

  // Publish to kafka for demo and async request
  const hasPublishRequest =
    demo ||
    (type === REQUEST_TYPE.API && responseType === RESPONSE_TYPE.DIRECT);
  if (hasPublishRequest)
    sendMessage(KAFKA_TOPIC.AI_VOICE_PUBLISH_TTS_FAILURE, {
      value: { errorMessage, errorCode, requestId, ...requestData },
    });

  if (request?.projectId) {
    if (type === REQUEST_TYPE.STUDIO) {
      // No using await here since we only need to save it without using its result
      // and avoid blocking response time
      updateBlockAudioLink({
        projectId: request.projectId,
        requestId: request._id,
        audioLink: null,
        status: BLOCK_SYNTHESIS_STATUS.FAILURE,
      }).catch((err) => {
        logger.error(err, {
          ctx: 'HandleJoinAudiosFailureResponse',
          requestId,
          projectId: request.projectId,
        });
      });
      return;
    }
    // Do not use await here to avoid blocking the response time
    // Use catch to avoid breaking the main process
    DubbingAdapter.updateProjectStatusFromRequest({
      projectId: request.projectId,
      requestStatus: REQUEST_STATUS.FAILURE,
      requestId,
      userId,
    }).catch((err) => {
      logger.error(err, { ctx: 'UpdateProjectStatusFromRequest', requestId });
    });
  }
};

const handleTtsSuccess = (request, preSendResponseAt) => {
  const {
    _id: requestId,
    userId,
    demo,
    type,
    responseType,
    audioLink,
    endedAt,
    processingAt,
    projectId,
  } = request;

  // Decrease number of pending and inprogress requests and Send to notification
  if (!demo) {
    processQueueWhenRequestSuccess({
      userId,
      requestId,
      requestType: type,
    });

    sendMessage(KAFKA_TOPIC.TTS_SUCCESS, {
      value: {
        requestId,
        projectId,
        userId,
        audioLink,
        preSendResponseAt,
        endedAt,
        processingAt,
      },
    });
  }

  const { title, text, sentences, paragraphs, ...requestData } = request;

  // Send callback for sync request api
  if (
    (type === REQUEST_TYPE.API || type === REQUEST_TYPE.API_CACHING) &&
    responseType === RESPONSE_TYPE.INDIRECT
  ) {
    sendApiResponse({
      request: { requestId, text, sentences, ...requestData },
    }).catch((e) => {
      logger.error(e, { ctx: 'SendApiResponseTtsSuccess', requestId });
    });
  }

  // Publish to kafka for demo and async request
  const hasPublishRequest =
    demo ||
    ((type === REQUEST_TYPE.API || type === REQUEST_TYPE.API_CACHING) &&
      responseType === RESPONSE_TYPE.DIRECT);
  if (hasPublishRequest)
    sendMessage(KAFKA_TOPIC.AI_VOICE_PUBLISH_TTS_SUCCESS, {
      value: { requestId, text, sentences, preSendResponseAt, ...requestData },
    });

  if (request?.projectId) {
    if (type === REQUEST_TYPE.STUDIO) {
      // No using await here since we only need to save it without using its result
      // and avoid blocking response time
      const audioExpiredAt = moment(request.endedAt).add(
        request.retentionPeriod,
        'days',
      );

      updateBlockAudioLink({
        projectId: request.projectId,
        requestId: request._id,
        audioLink,
        status: BLOCK_SYNTHESIS_STATUS.SUCCESS,
        audioExpiredAt,
      }).catch((err) => {
        logger.error(err, {
          ctx: 'HandleJoinAudiosSuccessResponse',
          requestId,
          projectId: request.projectId,
          audioLink,
        });
      });

      return;
    }
    // Do not use await here to avoid blocking the response time
    // Use catch to avoid breaking the main process
    DubbingAdapter.updateProjectStatusFromRequest({
      projectId: request.projectId,
      requestStatus: REQUEST_STATUS.SUCCESS,
      requestId,
      userId,
    }).catch((err) => {
      logger.error(err, { ctx: 'UpdateProjectStatusFromRequest', requestId });
    });
  }
};

/** publish Kafka event TTS_IN_PROGRESS  */
const handleUpdateProgressTTS = async ({
  requestId,
  userId,
  progress,
  processingAt,
}) => {
  const message = { value: { requestId, userId, progress, processingAt } };
  sendMessage(KAFKA_TOPIC.TTS_IN_PROGRESS, message);
};

const updateProgressWhenSynthesisSuccessInRedis = async ({
  requestId,
  userId,
  progress,
}) => {
  handleUpdateProgressTTS({ requestId, userId, progress });
};

const processRequestTimeOutFromCache = async ({ requestId, endedAt }) => {
  const finalRequest = await RequestCaching.updateRequestByIdInRedis(
    requestId,
    {
      status: REQUEST_STATUS.FAILURE,
      error: 'Request time out',
      endedAt,
      errorCode: code.TIMEOUT,
    },
  );

  updateFinalRequestFromRedisToDB(requestId, finalRequest);
  migrateTtsFromRedisToDB(requestId);

  return finalRequest;
};

const processRequestTimeOutFromDB = async ({ requestId, endedAt }) => {
  const requestInDB = await findRequestById(requestId);
  let finalRequest;
  if (requestInDB?.status === REQUEST_STATUS.IN_PROGRESS) {
    finalRequest = await updateRequestById(requestId, {
      status: REQUEST_STATUS.FAILURE,
      error: 'Request time out',
      endedAt,
      errorCode: code.TIMEOUT,
    });
  }
  return finalRequest;
};

const getFinalTimeOutRequest = async (requestId) => {
  const requestInRedis = await RequestCaching.findRequestByIdInRedis(requestId);
  const endedAt = new Date();

  // If requestInRedis is an empty object, it means that the request is not in Cache
  const isInprogressRequestInCache =
    !isEmptyObject(requestInRedis) &&
    requestInRedis.status === REQUEST_STATUS.IN_PROGRESS;

  const finalRequest = isInprogressRequestInCache
    ? await processRequestTimeOutFromCache({ requestId, endedAt })
    : await processRequestTimeOutFromDB({ requestId, endedAt });

  return finalRequest;
};

const handleRequestTimeOut = async (requestId) => {
  const finalRequest = await getFinalTimeOutRequest(requestId);
  if (!finalRequest) return;

  const processingTime = await ProcessingTimeCaching.getProcessingTime(
    requestId,
  );
  const currentStep = getCurrentStep(processingTime);
  const { startTime } = processingTime;

  const startCurrentStepAt = getStartTimeFromStep(processingTime, currentStep);

  await deleteInProgressRequest(requestId);

  await ProcessingTimeCaching.saveStepProcessingTime({
    requestId,
    step: currentStep,
    startTime: startCurrentStepAt,
  });

  saveProcessingTime({
    requestId,
    startTime,
    status: REQUEST_STATUS.FAILURE,
  });
  handleTtsFailure({
    request: finalRequest,
    errorCode: code.REQUEST_TIMEOUT,
  });
};

const handleUpdateProgressRequest = async ({
  requestId,
  status,
  progress,
  audioLink,
  error,
  errorCode,
}) => {
  const request = await RequestCaching.findRequestByIdInRedis(requestId);
  const { userId } = request;

  switch (status) {
    case REQUEST_STATUS.IN_PROGRESS: {
      await updateProgressWhenSynthesisSuccessInRedis({
        requestId,
        userId,
        progress,
      });
      break;
    }

    case REQUEST_STATUS.SUCCESS: {
      handleJoinAudiosSuccessResponse({
        requestId,
        audioLink,
      }).catch((err) => {
        logger.error({ err }, { ctx: 'HandleUpdateProgressRequest' });
      });
      break;
    }

    case REQUEST_STATUS.FAILURE: {
      handleJoinAudiosFailureResponse({
        requestId,
        error,
        errorCode,
        ttsRequestId: undefined,
      });
      break;
    }

    default:
      break;
  }
};

/** First check if the voice is in the VOICES array, then check if the voice is in the voice cloning collection */
const getTTSGateVoiceCode = async (voiceCode) => {
  const voice = global.VOICES.find((v) => v.code === voiceCode);
  if (voice) return voice?.ttsGateCode || voiceCode;

  const voiceInfo = await getVoiceCloningVoice(voiceCode);
  return voiceInfo?.ttsGateCode || voiceCode;
};

// TODO: Pass params request so that we can use instead of query in db
/** call to TTS-Gate for synthesis */
const callApiSynthesis = async (requestId) => {
  try {
    const request = await findRequestById(requestId);
    const {
      text,
      title,
      sentences,
      audioType,
      bitrate,
      sampleRate,
      speed: speedRate,
      fromVn,
      backgroundMusic,
      retentionPeriod,
      userId,
      type,
      /** only exist with dubbing request */
      subtitleLink,
      demo,
      processingAt,
      sentencesVoiceCode,
      returnTimestampWords,
      synthesisComputePlatform,
      isPriority,
      isRealTime,
    } = request;
    let voice = request.voice || {};
    const user = await findUser({ _id: userId });

    // check user role to have access to features (VoiceCloning, InstantVoiceCloning)
    const studioUsageOptions = await getPackageUsageOptions({
      userId,
      packageCode: user.packageCode,
      userUsageOptions: user,
    });
    const hasVoiceCloningFeatures = studioUsageOptions?.features?.includes(
      PACKAGE_FEATURE.AI_VOICE_CLONING,
    );
    const hasInstantVoiceCloningFeatures =
      studioUsageOptions?.features?.includes(
        PACKAGE_FEATURE.AI_INSTANT_VOICE_CLONING,
      );

    // ADVISE: a config value based on user, should be in User's role
    const synthesisVoiceCode =
      getFeatureValue(FEATURE_KEYS.VOICE_CLONING, { userId }) ||
      hasVoiceCloningFeatures ||
      hasInstantVoiceCloningFeatures;
    const hasVoiceInRequest = Object.keys(voice).length > 0;

    // The findRequestById function won't return the voice if voice cloning is used,
    // because it only looks up the voice in the voice collection.
    // Therefore, if voice cloning is enabled, we need to retrieve the voice from Redis.
    if (synthesisVoiceCode && !hasVoiceInRequest) {
      const requestCached = await RequestCaching.findRequestByIdInRedis(
        requestId,
      );
      voice = requestCached.voice;
    }

    // ADVISE: wasted business? we already have dictionary and clientPause
    const dictionary = await findDictionary(request.userId);
    const clientPause = await findClientPause(request.userId);
    const { paragraphBreak, sentenceBreak, majorBreak, mediumBreak } =
      clientPause || {};

    // ADVISE: search for LOADING_SYNTHESIS_FACTOR.START_PROCESSING, should have separated mechanism to update request progress. The updateRequestByIdInRedis() patch the whole request object by 2 get/set roundtrips, which is costly
    // Update progress
    const progress = LOADING_SYNTHESIS_FACTOR.START_PROCESSING * 100;
    RequestCaching.updateRequestByIdInRedis(requestId, { progress });
    await handleUpdateProgressTTS({
      requestId,
      userId,
      progress,
      processingAt,
    });

    // #region format data before sending to tts-api (gateway)

    const newSentences = !text
      ? await Promise.all(
          sentences.map(
            // ADVISE: IMPORTANT model: define Sentence type when moving to TypeScript
            async ({
              elements,
              characters,
              text: textSentence,
              speed: speedSentence,
              voiceCode,
              isMonetizable,
              credits,
              ...rest
            } = {}) => ({
              inputText: textSentence,
              speedRate: speedSentence,
              voiceCode: await getTTSGateVoiceCode(voiceCode),
              synthesisComputePlatform: getSynthesisComputePlatform({
                voiceCode,
                userId,
                type,
                demo,
              }),
              ...rest,
            }),
          ),
        )
      : [];

    const { link, volume } = backgroundMusic || {};
    const ttsGateVoiceCode = await getTTSGateVoiceCode(voice?.code);
    const commonPayload = {
      title,
      appId: AI_VOICE_APP_ID,
      callbackUrl: `${AI_VOICE_CALLBACK_URL}/api/v1/tts/callback-tts-gate`,
      callbackUpdateProgressUrl: demo
        ? `${AI_VOICE_CALLBACK_URL}/api/v1/tts/update-request-progress`
        : null,
      responseType: RESPONSE_TYPE.INDIRECT,
      audioType,
      bitrate: bitrate % 1000 === 0 ? bitrate / 1000 : bitrate,
      sampleRate: sampleRate ? parseInt(sampleRate, 10) : null,
      fromVn,
      voiceCode: ttsGateVoiceCode,
      retentionPeriod,
      clientUserId: userId,
      sessionId: requestId,
      speedRate,
      synthesisCcr: user?.concurrentRequest,
    };

    const ttsPayload = {
      ...commonPayload,

      inputText: text,
      sentences: text ? null : newSentences,
      backgroundMusic: { link, volume },
      dictionary: dictionary?.words || null,
      // ADVISE: user can use clientPause or not, should prepare in the request beforehand
      clientPause: studioUsageOptions?.features?.includes(
        PACKAGE_FEATURE.CLIENT_PAUSE,
      )
        ? { paragraphBreak, sentenceBreak, majorBreak, mediumBreak }
        : {},
      returnTimestamp: returnTimestampWords,
      isPriority: isPriority || undefined,
      isRealTime: isRealTime || undefined,
      synthesisComputePlatform,
    };

    const dubbingPayload = {
      ...commonPayload,

      subtitleLink,
      sentencesVoiceCode,
      synthesisComputePlatform,
    };

    // #endregion

    // ADVISE: we can check for type === REQUEST_TYPE.DUBBING earlier, then process payload separately. split the func smaller
    const apiEndpoint = type === REQUEST_TYPE.DUBBING ? 'dubbing' : 'tts';

    const res = await TtsGateAdapter.executeWithRetry({
      apiEndpoint,
      data: type === REQUEST_TYPE.DUBBING ? dubbingPayload : ttsPayload,
      requestId,
    });

    if (res.status !== 1) {
      handleJoinAudiosFailureResponse({
        requestId,
        error: res.errorMessage,
        errorCode: res.errorCode,
        ttsRequestId: undefined,
      });
      throw new Error(res.message);
    }
  } catch (error) {
    logger.error(error, { cxt: 'CallApiSynthesis', requestId });

    const errorMessage = JSON.stringify(error?.response?.data) || error.message;
    handleJoinAudiosFailureResponse({
      requestId,
      error: errorMessage,
      errorCode: error.code,
      ttsRequestId: undefined,
    });
  }
};

module.exports = {
  handleTtsDemoResponse,
  handleStreamAudio,
  handleTtsFailure,

  handleUpdateProgressTTS,

  handleRequestTimeOut,
  handleUpdateProgressRequest,
  callApiSynthesis,

  handleJoinAudiosFailureResponse,
  handleJoinAudiosSuccessResponse,
};
