const { default: mongoose } = require('mongoose');
const {
  getSearchQuery,
  getSortQuery,
  getSelectQuery,
  getDateQuery,
} = require('./util');
const { DIRECTION } = require('../../constants');

const find = async (
  model,
  {
    search,
    searchFields = [],
    dateField = 'createdAt',
    query,
    offset,
    limit,
    fields,
    sort,
  },
) => {
  const s = getSearchQuery(model, searchFields, search);

  // eslint-disable-next-line prefer-const
  let { startDate, endDate, ...dataQuery } = query || {};

  if (startDate || endDate) {
    const dateQuery = getDateQuery(dateField, query.startDate, query.endDate);
    dataQuery = { ...dataQuery, ...dateQuery };
  }

  const total = await model.countDocuments(
    search ? { $or: s, ...dataQuery } : dataQuery,
  );

  const documents = await model
    .find(search ? { $or: s, ...dataQuery } : dataQuery)
    .skip(offset || 0)
    .limit(limit || null)
    .sort(getSortQuery(sort))
    .select(fields ? getSelectQuery(fields) : {})
    .lean();

  return { documents, total };
};

const encodeCursor = (value) => {
  if (!value) return null;
  return Buffer.from(value.toString()).toString('base64');
};

const decodeCursor = (cursor) => {
  if (!cursor) return null;
  try {
    return Buffer.from(cursor, 'base64').toString('ascii');
  } catch (error) {
    throw new Error('Invalid cursor format');
  }
};

const findWithCursor = async (
  model,
  {
    search,
    searchFields = [],
    dateField = 'createdAt',
    query = {},
    cursor,
    limit = 10,
    fields,
    sort,
    cursorField = '_id', // field to make cursor, default is _id
    direction = DIRECTION.NEXT, // 'next' or 'prev'
  },
) => {
  const s = getSearchQuery(model, searchFields, search);

  // eslint-disable-next-line prefer-const
  let { startDate, endDate, ...dataQuery } = query || {};

  if (startDate || endDate) {
    const dateQuery = getDateQuery(dateField, query.startDate, query.endDate);
    dataQuery = { ...dataQuery, ...dateQuery };
  }

  let decodedCursor = null;
  if (cursor) {
    decodedCursor = decodeCursor(cursor);
    if (cursorField === '_id' && decodedCursor) {
      try {
        decodedCursor = new mongoose.Types.ObjectId(decodedCursor);
      } catch (error) {
        throw new Error('Invalid cursor ObjectId format');
      }
    }
  }

  let cursorQuery = {};
  if (decodedCursor) {
    const operator = direction === DIRECTION.NEXT ? '$gt' : '$lt';
    cursorQuery = { [cursorField]: { [operator]: decodedCursor } };
  }

  const finalQuery = {
    ...(search ? { $or: s } : {}),
    ...dataQuery,
    ...cursorQuery,
  };

  let sortQuery = getSortQuery(sort);

  if (!sortQuery[cursorField]) {
    sortQuery = {
      ...sortQuery,
      [cursorField]: direction === DIRECTION.NEXT ? 1 : -1,
    };
  }

  const documents = await model
    .find(finalQuery)
    .sort(sortQuery)
    .limit(limit + 1)
    .select(fields ? getSelectQuery(fields) : {})
    .lean();

  const hasMore = documents.length > limit;

  const resultDocuments = hasMore ? documents.slice(0, limit) : documents;

  let nextCursor = null;
  let prevCursor = null;

  if (resultDocuments.length) {
    const lastDoc = resultDocuments[resultDocuments.length - 1];
    const firstDoc = resultDocuments[0];

    nextCursor = hasMore ? encodeCursor(lastDoc[cursorField]) : null;
    prevCursor = cursor ? encodeCursor(firstDoc[cursorField]) : null;
  }

  return {
    documents: resultDocuments,
    pagination: {
      hasNextPage: direction === DIRECTION.NEXT ? hasMore : false,
      hasPreviousPage: direction === DIRECTION.PREV ? hasMore : Boolean(cursor),
      nextCursor,
      prevCursor,
      // count: resultDocuments.length,
    },
  };
};

module.exports = { find, findWithCursor };
