const { Constants } = require('@vbee-holding/vbee-tts-models');
const {
  REQUEST_TYPE,
  REQUEST_STATUS,
  V3_API_TYPE,
  API_VERSION,
} = require('../constants');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');

const requestDao = require('../daos/request');

const requestService = require('../services/request');
const requestProgressService = require('../services/requestProgress');
const audioDownloadService = require('../services/audioDownload');

const getRequests = async (req, res) => {
  const apiVersion = req.baseUrl.split('/')[2];
  const isV2Api = apiVersion === API_VERSION.V2;
  const { deviceInfo } = req;
  const deviceType = deviceInfo?.device?.type;
  const isSmartphone = deviceType === Constants.DEVICE.SMARTPHONE;
  const { search, fields, offset, limit, sort, type, notExistsProject } =
    req.query;
  const { userId } = req.user;

  const isRequestNotExistInProject =
    notExistsProject === undefined || notExistsProject === 'true';

  const query = {};
  switch (type) {
    case REQUEST_TYPE.API:
    case REQUEST_TYPE.DUBBING:
      query.query = { userId, type, hidden: { $ne: true } };
      break;
    case REQUEST_TYPE.STUDIO:
    default:
      query.query = {
        userId,
        demo: { $ne: true },
        type: REQUEST_TYPE.STUDIO,
        hidden: { $ne: true },
      };
      break;
  }
  if (search) query.search = search;
  if (fields) {
    query.fields = fields.split(',');

    // Add voiceCode field if not exists to get voiceCode for voice cloning
    if ((isSmartphone || isV2Api) && !query.fields.includes('voiceCode'))
      query.fields.push('voiceCode');
  }
  if (offset) query.offset = parseInt(offset, 10);
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');

  if (isRequestNotExistInProject) {
    query.query = { ...query.query, projectId: { $exists: false } };
    delete req.query.notExistsProject;
  }

  Object.keys(req.query)
    .filter(
      (q) => ['search', 'fields', 'offset', 'limit', 'sort'].indexOf(q) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { requests, total } =
    isSmartphone || isV2Api
      ? await requestService.getRequestsV2(query, userId)
      : await requestService.getRequests(query, userId);

  return res.send({ requests, metadata: { total } });
};

const getRequest = async (req, res) => {
  const apiVersion = req.baseUrl.split('/')[2];
  const { requestId } = req.params;
  const { userId } = req.user;
  const request =
    apiVersion === API_VERSION.V2
      ? await requestService.getRequestV2(requestId, userId)
      : await requestService.getRequest(requestId, userId);

  return res.send(request);
};

const getProgressRequest = async (req, res) => {
  const { requestId } = req.params;

  const { _id, progress, status, processingAt, endedAt, audioLink } =
    await requestProgressService.getProgress(requestId);

  return res.send({ _id, progress, status, processingAt, endedAt, audioLink });
};

const getApiRequest = async (req, res) => {
  const { requestId } = req.params;
  const { authorization } = req.headers;

  if (!authorization) throw new CustomError(errorCodes.UNAUTHORIZED);
  const [tokenType, accessToken] = authorization.split(' ');
  if (tokenType !== 'Bearer') throw new CustomError(errorCodes.UNAUTHORIZED);

  const request = await requestService.getApiRequest({
    requestId,
    appToken: accessToken,
  });

  return res.send(request);
};

const getV3ApiRequest = async (req, res) => {
  const { requestId } = req.params;

  const request = await requestDao.findRequest({ _id: requestId }, [
    'progress',
    'audioLink',
    'status',
    'v3ApiType',
  ]);
  const { progress, audioLink, status, v3ApiType } = request;

  if (v3ApiType === V3_API_TYPE.ARTICLE) {
    if (audioLink) return res.redirect(audioLink);
    return res.status(404).send('Not found');
  }
  const responseData = {
    statusCode: 0,
    message: status === REQUEST_STATUS.FAILURE ? 'Error!' : 'Success!',
    error: [],
    data: {
      pathAudio: audioLink,
      percentLoading: progress,
      status: status === REQUEST_STATUS.SUCCESS ? 2 : 0,
    },
    info: [],
  };

  return res.send(responseData);
};

const updateParagraphsOfRequest = async (req, res) => {
  const { requestId } = req.params;
  const { paragraphs } = req.body;

  await requestService.updateParagraphsOfRequest(requestId, paragraphs);
  return res.send({});
};

const deleteRequests = async (req, res) => {
  const { requestIds, isDeleteAll, type = REQUEST_TYPE.STUDIO } = req.body;
  const { userId } = req.user;

  if (isDeleteAll) {
    await requestDao.hideRequestsByUser(userId, type);
  } else {
    await requestDao.updateManyRequestsByIds(requestIds, {
      hidden: true,
    });
  }

  return res.send({});
};

const updateRequest = async (req, res) => {
  const { requestId } = req.params;
  const updateFields = req.body;

  const request = await requestService.updateRequest(requestId, updateFields);
  return res.send({ request });
};

const countPendingAndInProgressReq = async (req, res) => {
  const { userId, requestType } = req.query;
  const count = await requestService.countPendingAndInProgressReq({
    userId,
    requestType,
  });

  return res.send({ count });
};

const countPendingRequests = async (req, res) => {
  const { userId, requestType } = req.query;
  const count = await requestService.countPendingRequests({
    userId,
    requestType,
  });

  return res.send({ count });
};

const removePendingRequests = async (req, res) => {
  const { requestType, userId } = req.body;
  await requestService.removePendingRequestByTypes({
    requestType,
    userId,
  });

  return res.send({});
};

const setPendingAndInProgressReqCount = async (req, res) => {
  const { userId, requestType } = req.body;
  await requestService.setPendingAndInProgressReqCount({
    userId,
    requestType,
  });

  return res.send({});
};

const getAudio = async (req, res) => {
  const { requestId } = req.params;
  const { authorization } = req.headers;

  const audio = await audioDownloadService.getAudioDownloadUrl({
    requestId,
    authorization,
  });

  return res.send({ audio });
};

const getAudioDownloadUrl = async (req, res) => {
  const { requestId } = req.params;
  const { authorization } = req.headers;

  const audio = await audioDownloadService.handleDownloadAudio({
    requestId,
    authorization,
  });

  return res.send({ audio });
};

module.exports = {
  getRequests,
  getRequest,
  getProgressRequest,
  getApiRequest,
  getV3ApiRequest,
  updateParagraphsOfRequest,
  deleteRequests,
  updateRequest,
  countPendingAndInProgressReq,
  countPendingRequests,
  removePendingRequests,
  setPendingAndInProgressReqCount,
  getAudio,
  getAudioDownloadUrl,
};
