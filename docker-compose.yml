services:
  zookeeper:
    restart: always
    image: confluentinc/cp-zookeeper:7.5.0
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
      - ZOOKEEPER_TICK_TIME=2000
    volumes:
      - /etc/localtime:/etc/localtime:ro

  broker:
    restart: always
    image: confluentinc/cp-kafka:7.5.0
    container_name: broker
    ports:
      - "9092:9092"
      - "9093:9093"
    depends_on:
      - zookeeper
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - KAFKA_LISTENERS=PLAINTEXT://0.0.0.0:9092,EXTERNAL://0.0.0.0:9093
      - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,EXTERNAL:PLAINTEXT
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092
      - <PERSON><PERSON><PERSON>_LOG_RETENTION_HOURS=36
      - KAFKA_LOG_SEGMENT_BYTES=1073741824
      - KAFKA_LOG_RETENTION_BYTES=-1
      - KAFKA_NUM_PARTITIONS=6
      - KAFKA_DEFAULT_REPLICATION_FACTOR=1
      - KAFKA_MESSAGE_MAX_BYTES=10485760
      - KAFKA_REPLICA_FETCH_MAX_BYTES=10485760
      - KAFKA_MAX_REQUEST_SIZE=10485760
      - KAFKA_SOCKET_REQUEST_MAX_BYTES=10485760
      - KAFKA_HEAP_OPTS=-Xmx1G -Xms1G
    volumes:
      - /etc/localtime:/etc/localtime:ro

  redis:
    image: redis:6.2-alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - /etc/localtime:/etc/localtime:ro
