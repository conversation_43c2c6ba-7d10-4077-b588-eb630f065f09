const logger = require('../utils/logger');
const {
  REQUEST_STATUS,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  REQUEST_TYPE,
  TTS_STATUS,
} = require('../constants');

const Caching = require('.');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');
const { RandomFactory } = require('../utils/random');

/** create request cache, with TTL */
const createRequestInRedis = async (requestInfo) => {
  requestInfo = { ...requestInfo };
  if (!requestInfo._id) requestInfo._id = requestInfo.requestId;
  delete requestInfo.requestId;

  const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestInfo._id}`;
  const requestKeyTtl = REDIS_KEY_TTL.SYNTHESIS_REQUEST;

  await Caching.RedisRepo.set(
    requestKey,
    JSON.stringify(requestInfo),
    requestKeyTtl,
  );
};

// ADVISE: refactoring note: I keep the business of original code untouched. But this function return ambiguous result.
// when request is not found, it throws.
// When exception happen, it returns {}
/**
 * find temporary state of Request in Cache
 * @param {*} requestId
 * @returns throw if request is not found
 */
const findRequestByIdInRedis = async (requestId) => {
  try {
    const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestId}`;
    const request = await Caching.RedisRepo.get(requestKey);
    if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

    // @ts-ignore
    return JSON.parse(request);
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
    return {};
  }
};

// #region

/** get total count of TTS/Sentence (does not count silence sentence) of specific request */
const countRealTtsInRedis = async (requestId) => {
  const key = `${REDIS_KEY_PREFIX.TOTAL_TTS}_${requestId}`;
  let totalTtsRequest = await Caching.RedisRepo.get(key);
  if (totalTtsRequest) return Number(totalTtsRequest);

  const realTtsList = (await getTtsFromRequestIdInRedis(requestId)).filter(
    (tts) => !tts.silence,
  );
  totalTtsRequest = realTtsList.length || 1;

  await Caching.RedisRepo.set(key, totalTtsRequest, REDIS_KEY_TTL.SENTENCE);
  return totalTtsRequest;
};

/** extract tts entities from request */
const getTtsFromRequestIdInRedis = async (requestId, index) => {
  try {
    const request = await findRequestByIdInRedis(requestId);
    let { ttsIds = [] } = request || {};
    if (index) ttsIds = ttsIds.filter((tts) => tts.index === index);
    const ttsKeys = ttsIds.map(
      (tts) => `${REDIS_KEY_PREFIX.TTS}_${requestId}_${tts.ttsId}`,
    );
    if (!ttsKeys.length) return [];

    const ttsList = await Caching.RedisRepo.multiGet(ttsKeys);
    if (!ttsList?.length) return [];

    // @ts-ignore
    const ttsObjList = ttsList.map((tts) => JSON.parse(tts));
    return ttsObjList;
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
    return [];
  }
};

const saveTtsInRedis = async ({ requestId, index, ttsRequests }) => {
  ttsRequests = await Promise.all(
    ttsRequests.map(async (tts) => {
      const ttsId = RandomFactory.getGuid();
      tts._id = ttsId;

      const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
      const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

      Caching.RedisRepo.set(ttsKey, JSON.stringify(tts), ttsKeyTtl);
      return tts;
    }),
  );
  const ttsRequestIds = ttsRequests.map((tts) => {
    return {
      index,
      ttsId: tts._id,
      subIndex: tts.subIndex,
    };
  });

  const sentenceIndexKey = `${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${index}`;
  await Caching.RedisRepo.set(
    sentenceIndexKey,
    JSON.stringify(ttsRequestIds),
    REDIS_KEY_TTL.SENTENCE,
  );

  return ttsRequests;
};

const updateFailureTTSInRedis = async ({ requestId, ttsId, error }) => {
  const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
  const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

  const tts = await Caching.RedisRepo.get(ttsKey);
  if (!tts) return null;

  // @ts-ignore
  const ttsObj = JSON.parse(tts);
  const newTts = { ...ttsObj, status: TTS_STATUS.FAILURE, error };

  await Caching.RedisRepo.set(ttsKey, JSON.stringify(newTts), ttsKeyTtl);

  return newTts;
};

const checkSentenceExistInCache = async (requestId, sentenceIndex) => {
  const sentenceStatusKey = `${REDIS_KEY_PREFIX.SENTENCE_TOKENIZATION_STATUS}_${requestId}_${sentenceIndex}`;
  const sentenceStatus = await Caching.RedisRepo.get(sentenceStatusKey);

  return !!sentenceStatus;
};

const processTtsDubbingToJoinAudios = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);

  const subtitles = ttsList.map((tts) => ({
    start: tts.start,
    end: tts.end,
    audio_name: tts.audioName,
  }));

  return subtitles;
};

// #endregion

const updateCachePhrasesInRedis = async ({
  requestId,
  ttsId,
  index,
  subIndex,
  phrases,
}) => {
  const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
  const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

  try {
    const tts = await Caching.RedisRepo.get(ttsKey);
    if (!tts) return;

    // @ts-ignore
    const ttsObj = JSON.parse(tts);
    const newTts = {
      ...ttsObj,
      phrases,
      requestId,
      index,
      subIndex,
      status: TTS_STATUS.SUCCESS,
      t2aDuration: 0,
      synthesisDuration: 0,
    };

    await Caching.RedisRepo.set(ttsKey, JSON.stringify(newTts), ttsKeyTtl);
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
  }
};

/** patch request cache (by get and then re-set) */
const updateRequestByIdInRedis = async (requestId, updateFields) => {
  try {
    const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestId}`;
    const requestKeyTtl = REDIS_KEY_TTL.SYNTHESIS_REQUEST;

    let request = await Caching.RedisRepo.get(requestKey);
    if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

    // @ts-ignore
    request = JSON.parse(request);
    // @ts-ignore
    request = { ...request, ...updateFields };

    await Caching.RedisRepo.set(
      requestKey,
      JSON.stringify(request),
      requestKeyTtl,
    );

    return request;
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
    return {};
  }
};

/**
 * remove pending requests (with specific type)  of user
 * @param {*} param0
 */
const removePending = async ({ requestType, userId }) => {
  const pendingReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_REQUESTS}_${userId}`;

  const pendingAndInprReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

  await Caching.Manager.mdel([pendingReqKey, pendingAndInprReqKey]);
};

const getAudiosInRedis = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);

  const sortTtsList = ttsList.sort((a, b) => {
    return a.index - b.index || a.subIndex - b.subIndex;
  });

  // ADVISE: duplicated code const audios = tts.reduce((acc, curr)
  const audios = sortTtsList.reduce((acc, curr) => {
    const { audioLink, phrases = [] } = curr;
    if (audioLink) return [...acc, audioLink];

    const sortPhrases = phrases.sort((a, b) => a.index - b.index);
    const audioLinks = sortPhrases.map((phrase) => phrase.audioLink);
    return [...acc, ...audioLinks];
  }, []);

  return audios;
};

const saveAudioInRedis = async ({
  requestId,
  ttsId,
  audioLink,
  audioName,
  t2aDuration,
  synthesisDuration,
  phrases,
}) => {
  const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
  const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

  const tts = await Caching.RedisRepo.get(ttsKey);
  if (!tts) return;

  // @ts-ignore
  const ttsObj = JSON.parse(tts);
  const newTts = {
    ...ttsObj,
    audioName,
    audioLink,
    status: TTS_STATUS.SUCCESS,
    t2aDuration,
    synthesisDuration,
    phrases,
  };
  await Caching.RedisRepo.set(ttsKey, JSON.stringify(newTts), ttsKeyTtl);
};

const saveAudioLinkInRedis = async (requestId, audioLink) => {
  const endedAt = new Date();
  const request = await updateRequestByIdInRedis(requestId, {
    audioLink,
    status: REQUEST_STATUS.SUCCESS,
    endedAt,
  });

  return request;
};

const getSynthesisTimeInRedis = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);

  const t2aDurations = ttsList.reduce(
    (prev, curr) => (curr.t2aDuration ? [...prev, curr.t2aDuration] : prev),
    [],
  );
  const synthesisDurations = ttsList.reduce(
    (prev, curr) =>
      curr.synthesisDuration ? [...prev, curr.synthesisDuration] : prev,
    [],
  );

  return { t2aDurations, synthesisDurations };
};

const checkCompletedIndexInRedis = async (requestId, index) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId, index);

  const isNotCompleted = ttsList.some(
    (tts) => tts.status !== TTS_STATUS.SUCCESS,
  );
  return !isNotCompleted;
};

// ADVISE: about the naming, since this is a refactoring to move func here, I keep the old naming. In future, we can rename to find(), getTts() because we're already inside "RequestCaching" domain/scope (Request and Redis words are redundant)
module.exports = {
  createRequestInRedis,

  findRequestByIdInRedis,
  updateRequestByIdInRedis,
  removePending,

  updateCachePhrasesInRedis,

  countRealTtsInRedis,
  getTtsFromRequestIdInRedis,
  saveTtsInRedis,
  updateFailureTTSInRedis,
  checkSentenceExistInCache,

  processTtsDubbingToJoinAudios,

  getAudiosInRedis,
  saveAudioInRedis,
  saveAudioLinkInRedis,

  getSynthesisTimeInRedis,
  checkCompletedIndexInRedis,
};
