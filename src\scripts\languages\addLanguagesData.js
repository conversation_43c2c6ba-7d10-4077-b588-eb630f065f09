require('dotenv').config();

const { initMongoDB } = require('../../models');
const Language = require('../../models/language');
const logger = require('../../utils/logger');
const languagesData = require('./languagesData.json');

global.logger = logger;

const updateNameAndRegion = async () => {
  try {
    await initMongoDB();
    const bulkOps = [];
    for (const lang of languagesData) {
      bulkOps.push({
        updateOne: {
          filter: { code: lang.code },
          update: {
            $set: { name: lang.name, region: lang.region },
          },
        },
      });
    }
    await Language.bulkWrite(bulkOps);
    logger.info(`Updated name and region for languages`, {
      ctx: 'RunScript',
    });
  } catch (error) {
    logger.error('Update name and region for language failed', {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting update name and region for languages...`, {
    ctx: 'RunScript',
  });
  await updateNameAndRegion();
  logger.info(`Update name and region for languages successfully`, {
    ctx: 'RunScript',
  });
  process.exit(0);
})();
