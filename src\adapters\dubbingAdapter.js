const CustomError = require('../errors/CustomError');
const code = require('../errors/code');
const { VBEE_DUBBING_URL } = require('../configs');

const { FEATURE_KEYS } = require('../constants/featureKeys');

const callApi = require('../utils/callApi');
const logger = require('../utils/logger');

const { getFeatureValue } = require('../services/growthbook');

const { getAccessToken } = require('../services/iam');

const createProject = async ({
  userId,
  title,
  speed,
  voiceCode,
  subtitleLink,
  originalInfo,
  targetLanguage,
}) => {
  try {
    const isMultipleInputDubbing = getFeatureValue(
      FEATURE_KEYS.MULTIPLE_INPUT_DUBBING,
      { userId },
    );

    const projectData = {
      userId,
      title,
      speed,
      voiceCode,
      currentSubtitleLink: subtitleLink,
    };

    if (isMultipleInputDubbing) {
      projectData.originalInfo = originalInfo;
      projectData.targetLanguage = targetLanguage;
    }

    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects`,
      method: 'POST',
      data: projectData,
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new Error('Failed to create dubbing project');
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'CreateProject', userId });
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Failed to create dubbing project',
    );
  }
};

const getListProjects = async ({
  userId,
  search,
  fields,
  offset,
  limit,
  sort,
  status: projectStatus,
  startDate,
  endDate,
}) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects`,
      method: 'GET',
      params: {
        userId,
        search,
        fields,
        offset,
        limit,
        sort,
        status: projectStatus,
        startDate,
        endDate,
      },
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new Error('Failed to get list projects');
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'GetListProjects', userId });
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Failed to get list projects',
    );
  }
};

const updateProject = async (projectId, updatedFields) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects/${projectId}`,
      method: 'PUT',
      data: updatedFields,
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new Error('Failed to update project');
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'UpdateProject', projectId });
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Failed to update project',
    );
  }
};

const updateProjectStatusFromRequest = async ({
  projectId,
  requestStatus,
  requestId,
  userId,
}) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects/${projectId}/status`,
      method: 'PUT',
      data: { status: requestStatus, requestId, userId },
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new Error();
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'UpdateProjectStatus', projectId });
    throw new CustomError(code.UPDATE_PROJECT_STATUS_FAILURE);
  }
};

const callApiDubbingDeleteProjects = async ({
  userId,
  projectIds,
  isDeleteAll,
}) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects`,
      method: 'DELETE',
      data: {
        userId,
        projectIds,
        isDeleteAll,
      },
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new CustomError(code.INTERNAL_SERVER_ERROR);
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'DeleteProjects', userId });
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Failed to delete projects',
    );
  }
};

const callApiDubbingGetProject = async ({ projectId, userId }) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects/${projectId}`,
      method: 'GET',
      params: { userId },
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new Error();
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'GetProject', projectId, userId });
    throw new CustomError(code.GET_PROJECT_FAILURE, 'Failed to get project');
  }
};

const getLanguages = async (query) => {
  const { status, result } = await callApi({
    url: `${VBEE_DUBBING_URL}/api/v1/languages`,
    method: 'GET',
    params: query,
  });

  if (status !== 1) throw new CustomError(code.INTERNAL_SERVER_ERROR);

  return result;
};

module.exports = {
  createProject,

  getListProjects,

  updateProject,
  updateProjectStatusFromRequest,

  callApiDubbingDeleteProjects,
  callApiDubbingGetProject,
  getLanguages,
};
