const dubbingService = require('../services/dubbing');
const DubbingAdapter = require('../adapters/dubbingAdapter');

const handleDubbing = async (req, res) => {
  const { publicIP: ip } = req.__clientInfo;
  const { device, deviceInfo } = req;
  const {
    title,
    speed,
    bitrate,
    volume,
    voiceCode,
    audioType,
    subtitleLink,
    projectId,
    sentencesVoiceCode,
  } = req.body;
  const { userId, email, phoneNumber } = req.user;

  const request = await dubbingService.handleDubbing({
    ip,
    device,
    deviceInfo,
    userId,
    email,
    phoneNumber,
    title,
    speed,
    bitrate,
    volume,
    voiceCode,
    audioType,
    subtitleLink,
    projectId,
    sentencesVoiceCode,
  });
  return res.send(request);
};

const handleDubbingWithVideo = async (req, res) => {
  const { publicIP: ip } = req.__clientInfo;
  const {
    title,
    speed,
    bitrate,
    volume,
    voiceCode,
    audioType,
    linkVideo,
    source,
    videoDuration,
    originalLanguage,
  } = req.body;
  const { userId, email, phoneNumber } = req.user;

  const request = await dubbingService.handleDubbingWithVideo({
    ip,
    userId,
    email,
    phoneNumber,
    title,
    speed,
    bitrate,
    volume,
    voiceCode,
    audioType,
    linkVideo,
    source,
    videoDuration,
    originalLanguage,
  });

  return res.send(request);
};

const countSubtitleCharacters = async (req, res) => {
  const { subtitleLink } = req.query;
  const characters = await dubbingService.countSubtitleCharacters(subtitleLink);

  return res.send({ characters });
};

const createProject = async (req, res) => {
  const { userId } = req.user;
  const {
    title,
    speed,
    voiceCode,
    subtitleLink,
    originalInfo,
    targetLanguage,
  } = req.body;

  const project = await DubbingAdapter.createProject({
    userId,
    title,
    speed,
    voiceCode,
    subtitleLink,
    originalInfo,
    targetLanguage,
  });

  return res.send(project);
};

const getProjects = async (req, res) => {
  const { userId } = req.user;
  const { search, fields, offset, limit, sort, status, startDate, endDate } =
    req.query;

  const projects = await DubbingAdapter.getListProjects({
    userId,
    search,
    fields,
    offset,
    limit,
    sort,
    status,
    startDate,
    endDate,
  });

  return res.send(projects);
};

const updateProject = async (req, res) => {
  const { projectId } = req.params;
  const updatedFields = req.body;

  const project = await DubbingAdapter.updateProject(projectId, updatedFields);

  return res.send(project);
};

const deleteProjects = async (req, res) => {
  const { userId } = req.user;
  const { projectIds, isDeleteAll } = req.body;

  await dubbingService.deleteProjects({ userId, projectIds, isDeleteAll });

  return res.send({});
};

const getProject = async (req, res) => {
  const { projectId } = req.params;
  const { userId } = req.user;

  const project = await dubbingService.getProject({ projectId, userId });

  return res.send(project);
};

const getLanguages = async (req, res) => {
  const { query } = req;
  const languages = await DubbingAdapter.getLanguages(query);

  return res.send(languages);
};

module.exports = {
  handleDubbing,
  countSubtitleCharacters,
  handleDubbingWithVideo,
  createProject,
  getProjects,
  updateProject,
  deleteProjects,
  getProject,
  getLanguages,
};
