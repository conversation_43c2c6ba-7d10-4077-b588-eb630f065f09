const router = require('express').Router();
const asyncMiddleware = require('../../middlewares/async');
const languageController = require('../../controllers/languages');
const { auth } = require('../../middlewares/auth');

/* eslint-disable prettier/prettier */
router.get('/languages', auth, asyncMiddleware(languageController.getLanguages));
/* eslint-disable prettier/prettier */

module.exports = router;
