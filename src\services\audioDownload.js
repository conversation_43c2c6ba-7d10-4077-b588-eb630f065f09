const moment = require('moment');

const {
  DEFAULT_BUCKET_S3,
  UPLOAD_URL,
  AUDIO_URL_EXPIRES,
  AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY,
  VBEE_URL,
  GCS_AICORE_TTS_CLIENT_EMAIL,
  GCS_AICORE_TTS_PRIVATE_KEY,
} = require('../configs');
const {
  REDIS_KEY_PREFIX,
  AUDIO_URL_TYPE,
  REQUEST_TYPE,
  PACKAGE_CODE,
} = require('../constants');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { STORAGE } = require('../constants/cloudStorage');

const CustomError = require('../errors/CustomError');
const code = require('../errors/code');

const logger = require('../utils/logger');
const { RandomFactory } = require('../utils/random');
const callApi = require('../utils/callApi');

const { getFeatureValue } = require('./growthbook');

const { getAwsZone } = require('../daos/awsZone');
const requestDao = require('../daos/request');
const userDao = require('../daos/user');
const {
  increaseDownloadCount,
  updateDownloadStats,
} = require('../daos/request');

const Caching = require('../caching');

const { getKeyFromS3Url } = require('./s3');
const {
  getKeyFromGCSUrl,
  checkFileExists,
  copyGCStoGCSWithRetry,
} = require('./googleCloudStorage');
const { detectCloudStorageService } = require('./audio');
const { getPackageUsageOptions } = require('./package');

// #region PRIVATE FUNCS

const getPresignedUrl = async ({ key, bucket, authorization, storage }) => {
  try {
    const clientEmail = GCS_AICORE_TTS_CLIENT_EMAIL;
    const privateKey = Buffer.from(
      GCS_AICORE_TTS_PRIVATE_KEY,
      'utf-8',
    ).toString('base64');

    const res = await callApi({
      headers: {
        authorization,
        'access-key-id': AWS_ACCESS_KEY_ID,
        'secret-access-key': AWS_SECRET_ACCESS_KEY,
        ...(storage === STORAGE.GCS && {
          'client-email': clientEmail,
        }),
        ...(storage === STORAGE.GCS && {
          'private-key': privateKey,
        }),
      },
      url: `${UPLOAD_URL}/api/v1/files/presigned-url-for-sharing`,
      method: 'GET',
      params: {
        key,
        bucket,
        expiresIn: AUDIO_URL_EXPIRES,
        ...(storage && { storage }),
      },
    });
    return res.result;
  } catch (error) {
    logger.error(error, { ctx: 'FetchAudioURL' });
    return null;
  }
};

const getPresignedAudioUrl = async (request, authorization) => {
  const { awsZoneFunctions = {}, googleCloudStorage = {} } = request;

  const { s3Bucket = DEFAULT_BUCKET_S3 } = awsZoneFunctions;
  const { bucket: gcsBucket } = googleCloudStorage;

  const storage = detectCloudStorageService(request.audioLink);
  const isGCSStorage = storage === STORAGE.GCS;

  const key = isGCSStorage
    ? getKeyFromGCSUrl(request.audioLink)
    : getKeyFromS3Url(request.audioLink);

  const bucket = isGCSStorage ? gcsBucket : s3Bucket;

  const presignedRes = await getPresignedUrl({
    key,
    bucket,
    authorization,
    storage,
  });

  return presignedRes?.url;
};

const ensureAudioFileExists = async (request) => {
  const recopyAudioFile = getFeatureValue(FEATURE_KEYS.RECOPY_AUDIO_FILE, {
    userId: request?.userId,
    requestType: request?.type,
  });
  if (!recopyAudioFile) return;

  const { audioLink, sourceAudioLink, googleCloudStorage = {} } = request;

  const storage = detectCloudStorageService(audioLink);
  const isGCSStorage = storage === STORAGE.GCS;
  if (!isGCSStorage) return;

  const { bucket: gcsBucket } = googleCloudStorage;
  const key = getKeyFromGCSUrl(audioLink);
  const isFileExists = await checkFileExists({ bucket: gcsBucket, key });
  if (isFileExists) return;

  logger.warn('Audio file does not exist', {
    ctx: 'ensureAudioFileExists',
    requestId: request._id,
    userId: request.userId,
    sourceUrl: sourceAudioLink,
    destinationUrl: audioLink,
  });

  await copyGCStoGCSWithRetry({ bucket: gcsBucket, key, url: sourceAudioLink });
};

const getAudioUrl = async ({ request, authorization }) => {
  // ADVISE: do we really need to get audioUrlType from feature flag?
  const audioUrlType = getFeatureValue(FEATURE_KEYS.AUDIO_URL, {
    userId: request.userId,
    appId: request.app,
    requestType: request.type,
  });

  if (audioUrlType !== AUDIO_URL_TYPE.ORIGINAL_URL) {
    // TODO: Temporary disable with original url type (hn.vbee.vn), but still need handle soon
    await ensureAudioFileExists(request);
  }

  switch (audioUrlType) {
    case AUDIO_URL_TYPE.ORIGINAL_URL: {
      return request.audioLink;
    }

    case AUDIO_URL_TYPE.SHORT_URL: {
      const audioUrl = await getPresignedAudioUrl(request, authorization);
      if (!audioUrl) return null;

      // Cache key `${request_id}_${random_string}` with value <presigned_url> in Cache with TTL equals to presigned_url's expires time
      const randomString = RandomFactory.getGuid();
      const requestId = request._id;
      const cacheKey = `${REDIS_KEY_PREFIX.AUDIO_URL}_${requestId}_${randomString}`;
      const cacheKeyTtl = AUDIO_URL_EXPIRES;
      await Caching.RedisRepo.set(cacheKey, audioUrl, cacheKeyTtl);

      // Return short url
      return `${VBEE_URL}/s/${requestId}/${randomString}`;
    }

    case AUDIO_URL_TYPE.PRESIGNED_URL:
    default: {
      const audioUrl = await getPresignedAudioUrl(request, authorization);
      return audioUrl;
    }
  }
};

const getFirstDownloadDuration = (endedAt) => {
  const now = moment();
  const duration = now.diff(moment(endedAt), 'seconds');
  return duration;
};

const handleUpdateDownloadStats = async ({
  requestId,
  downloadCount,
  endedAt,
}) => {
  const hasDownloaded = downloadCount > 0;
  if (hasDownloaded) {
    await increaseDownloadCount(requestId);
  } else {
    const firstDownloadDuration = getFirstDownloadDuration(endedAt);
    await updateDownloadStats(requestId, firstDownloadDuration);
  }
};

const getCloudFrontUrl = ({ cloudFrontDomain, url }) => {
  if (!cloudFrontDomain) return url;

  const urlObject = new URL(url);
  const s3Domain = urlObject.hostname;
  return url.replace(s3Domain, cloudFrontDomain);
};

const getLatestDownloadedAt = (user, requestType) => {
  let latestDownloadedAt;

  switch (requestType) {
    case REQUEST_TYPE.STUDIO: {
      latestDownloadedAt = user.latestDownloadedAt;
      break;
    }

    case REQUEST_TYPE.DUBBING: {
      latestDownloadedAt = user.dubbing?.latestDownloadedAt;
      break;
    }

    default:
      break;
  }

  return latestDownloadedAt;
};

const checkAllowDownload = (latestDownloadedAt) => {
  const hasDownloaded = !!latestDownloadedAt;
  const now = moment();
  const hasDownloadedToday = now.isSame(moment(latestDownloadedAt), 'day');

  const allowDownload = !hasDownloaded || !hasDownloadedToday;
  return allowDownload;
};

const updateLatestDownloadedAt = async (user, requestType) => {
  const userId = user._id;

  const isStudioFreeV2Package =
    requestType === REQUEST_TYPE.STUDIO
      ? user.packageCode === PACKAGE_CODE.STUDIO_FREE_V2
      : checkUseStudioFreeV2Package(user);

  if (isStudioFreeV2Package)
    await userDao.updateStudioLatestDownloadedAt(userId);
  else {
    switch (requestType) {
      case REQUEST_TYPE.STUDIO: {
        await userDao.updateLatestDownloadedAt(userId);
        break;
      }
      case REQUEST_TYPE.DUBBING: {
        await userDao.updateDubbingLatestDownloadedAt(userId);
        break;
      }

      default:
        break;
    }
  }
};

const checkIsLimitDownloadAudio = async (user) => {
  const studioUsageOptions = await getPackageUsageOptions({
    userId: user?._id,
    packageCode: user?.packageCode,
    userUsageOptions: user,
  });
  const studioUserDownload = studioUsageOptions?.download;

  return studioUserDownload >= 0;
};

/** Check if user has free dubbing package or has expiry dubbing package and studio-free-v2 package */
const checkUseStudioFreeV2Package = (user) => {
  const isExpireDubbingPackage = moment().isAfter(
    user.dubbing?.packageExpiryDate,
  );
  const isUsingFreeDubbingPackage = !user.dubbing?.packageExpiryDate;
  const isUsingStudioFreeV2Package =
    user.packageCode === PACKAGE_CODE.STUDIO_FREE_V2;

  const isFreePackage =
    (isUsingFreeDubbingPackage || isExpireDubbingPackage) &&
    isUsingStudioFreeV2Package;

  return isFreePackage;
};

// #endregion

/**
 *
 * @description {string} param requestId
 * @description {string} param authorization need Bearer prefix
 * @returns
 */
const getAudioDownloadUrl = async ({ requestId, authorization }) => {
  const request = await requestDao.findRequestById(requestId);
  const { retentionPeriod, endedAt, downloadCount = 0 } = request;
  const expiresTime = moment(endedAt).add(retentionPeriod, 'days');
  if (moment().isAfter(expiresTime))
    throw new CustomError(code.AUDIO_URL_EXPIRED);

  let audioUrl = await getAudioUrl({ request, authorization });
  if (audioUrl) {
    // Update download stats. Async because we don't want to wait for this when return audio link
    handleUpdateDownloadStats({ requestId, downloadCount, endedAt });

    // ADVISE: do we really need to get feature flag useCloudFrontAsProxy here? what is the case for NOT use CloudFront?
    // if there is a reason for that, explain the business logic
    const useCloudFrontAsProxy = getFeatureValue(
      FEATURE_KEYS.CLOUD_FRONT_AS_PROXY,
      { userId: request.userId, appId: request.app, requestType: request.type },
    );

    if (useCloudFrontAsProxy) {
      const awsZone = await getAwsZone({ region: request.awsZoneSynthesis });
      audioUrl = getCloudFrontUrl({
        cloudFrontDomain: awsZone.s3CloudFront?.[request.retentionPeriod],
        url: audioUrl,
      });
    }

    // Return audio url
    return audioUrl;
  }
  throw new CustomError(code.BAD_REQUEST);
};

/** Use to limit download per day with user */
const handleDownloadAudio = async ({ requestId, authorization }) => {
  const request = await requestDao.findRequestById(requestId);
  const { userId, type: requestType } = request;

  const user = await userDao.findUserById(userId);

  const latestDownloadedAt = getLatestDownloadedAt(user, requestType);
  const isLimitDownloadAudio = await checkIsLimitDownloadAudio(user);

  // At this moment, only free packages (Studio or Dubbing) limit downloads per day, and it's 1 download per day { download = 1 }
  // So we only check for free package, and assume max download is 1
  // In the future, if the limit increases, we need to update this logic
  if (isLimitDownloadAudio) {
    const allowDownload = checkAllowDownload(latestDownloadedAt);

    if (allowDownload) {
      const audioUrl = await getAudioDownloadUrl({ requestId, authorization });
      await updateLatestDownloadedAt(user, requestType);
      // sendEventDownloadToDataSenses(request, datasenses);
      return audioUrl;
    }

    throw new CustomError(code.DOWNLOAD_QUOTA_EXCEEDED);
  } else {
    const audioUrl = await getAudioDownloadUrl({ requestId, authorization });
    // sendEventDownloadToDataSenses(request, datasenses);
    return audioUrl;
  }
};

module.exports = {
  getAudioDownloadUrl,
  handleDownloadAudio,
};
