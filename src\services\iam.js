const snakeCaseKeys = require('snakecase-keys');
const {
  IAM_URL,
  IAM_REALM,
  IAM_CLIENT_ID,
  IAM_CLIENT_SECRET,
} = require('../configs');
const callApi = require('../utils/callApi');
const logger = require('../utils/logger');

const getPublicKey = async () => {
  // ADVISE: create IamAdapter
  let { publicKey } = await callApi({
    method: 'GET',
    url: `${IAM_URL}/auth/realms/${IAM_REALM}`,
  });
  publicKey = `-----BEGIN PUBLIC KEY-----\r\n${publicKey}\r\n-----END PUBLIC KEY-----`;
  return publicKey;
};

/** serviceA has client, serviceB need token (issued by Keycloak) to call to serviceA */
const getAccessToken = async () => {
  if (global.IAM_ACCESS_TOKEN) return global.IAM_ACCESS_TOKEN;

  const data = {
    clientId: IAM_CLIENT_ID,
    clientSecret: IAM_CLIENT_SECRET,
    grantType: 'client_credentials',
  };

  try {
    // ADVISE: create IamAdapter
    const { accessToken } = await callApi({
      method: 'POST',
      url: `${IAM_URL}/auth/realms/${IAM_REALM}/protocol/openid-connect/token`,
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      data: new URLSearchParams(snakeCaseKeys(data)),
    });

    return accessToken;
  } catch (error) {
    logger.error(`Get IAM access token failed`);
    return null;
  }
};

module.exports = { getPublicKey, getAccessToken };
