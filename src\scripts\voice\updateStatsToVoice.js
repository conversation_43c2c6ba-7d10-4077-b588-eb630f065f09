require('dotenv').config();
const moment = require('moment');
const { COMMUNITY_VOICE_STATS_EVENT } = require('../../constants');
const { getAllVoiceCodes } = require('../../daos/communityVoiceStats');
const { initMongoDB } = require('../../models');

const logger = require('../../utils/logger');
const {
  handleCommunityVoiceStats,
} = require('../../services/communityVoiceStats');
const caching = require('../../caching');

global.logger = logger;

(async () => {
  await Promise.all([caching.init(), initMongoDB()]);

  logger.info(`Starting sync voice stats to voice...`, {
    ctx: 'RunScriptSyncVoiceStatsToVoice',
  });

  const date = moment().subtract(1, 'day').format('YYYY-MM-DD');

  const voiceCodes = await getAllVoiceCodes();

  logger.info(`Voice codes: ${voiceCodes.length}`, {
    ctx: 'RunScriptSyncVoiceStatsToVoice',
  });

  await handleCommunityVoiceStats({
    date,
    voiceCodes,
    event: COMMUNITY_VOICE_STATS_EVENT.SYNC_VOICE_STATS_TO_VOICE,
  });

  logger.info(`Sync voice stats to voice successfully`, {
    ctx: 'RunScriptSyncVoiceStatsToVoice',
  });
  process.exit(1);
})();
