const moment = require('moment');
const {
  PACKAGE_FEATURE,
  REQUEST_TYPE,
  KAFKA_TOPIC,
  REQUEST_STATUS,
  SYNTHESIS_TYPE,
  REGEX,
  TTS_CORE_VERSION,
  REDIS_KEY_PREFIX,
  SERVICE_TYPE,
  TTS_PROCESSING_STEPS,
  DEFAULT_BITRATE,
  VOICE_TYPE,
} = require('../constants');
const {
  DEFAULT_NORMALIZER_FUNCTION,
  DEFAULT_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_T2A_FUNCTION,
  DEFAULT_SYNTHESIS_FUNCTION,
  DEFAULT_JOIN_AUDIO_FUNCTION,
  DEFAULT_BUCKET_S3,
  DEFAULT_AWS_REGION,
  MULTI_ZONE,
  TEXT_DEMO_MAX_LENGTH,
  DEFAULT_AUDIO_TYPE,
} = require('../configs');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { DEFAULT_CLIENT_PAUSE } = require('../constants/clientPause');
const { RATE_LIMIT_TYPE } = require('../constants/rateLimiter');

const CustomError = require('../errors/CustomError');
const code = require('../errors/code');

const { checkVoicePermission, canUseEOLVoice } = require('../utils/tts');
const logger = require('../utils/logger');
const { RandomFactory } = require('../utils/random');

const { sendMessage } = require('./kafka/producer');

const RequestCaching = require('../caching/requestCaching');
const ProcessingTimeCaching = require('../caching/processingTimeCaching');

const MoEngageAdapter = require('../adapters/moengage');
const { getFeatureValue } = require('./growthbook');

const {
  findUser,
  updateUserById,
  increaseUsedFreeInstantVoiceSynthesis,
} = require('../daos/user');
const { findDictionary } = require('../daos/dictionary');
const { getAwsZone } = require('../daos/awsZone');
const { updateBlockRequestId, findProject } = require('../daos/project');

const {
  validateText,
  getValidSampleRates,
  validateCredits,
  countCreditsByVoiceFactor,
  validateWalletCredits,
} = require('./preprocessing');
const { spendCharacters } = require('./characterProcessing');
const { callApiSynthesis } = require('./ttsProcessing');

const { enqueueRequestAndTryToConsume } = require('./queue');
const { createRequest, getTitle, checkMonetizable } = require('./request');
const { getVersionVoice, getVoiceInfoByCode } = require('./voice');
const {
  checkSpamAccount,
  isAdvancedGlobalVoice,
  shouldLockOneTimeCredits,
} = require('./user');
const { getSynthesisComputePlatform } = require('./synthesisComputePlatform');
const { getPackageUsageOptions, checkFreePackage } = require('./package');
const { getClientPause } = require('./clientPause');

const { SynthesisService } = require('./SynthesisService');
const { RateLimitService } = require('./RateLimitService');
const BannedAccountService = require('./BannedAccountService');
const dictionaryService = require('./dictionary');

const { getAwsZoneSynthesis } = require('./init/awsZone');

// ADVISE: IMPORTANT: the func is too long (really hard to understand/modify/test), split it into smaller funcs
/** main service func to handle synthesis request from controller */
const handleSynthesisRequest = async ({
  ip,
  device,
  deviceInfo,
  title = '',
  text,
  paragraphs,
  voiceCode,
  sentences = [],
  clientPause,
  backgroundMusic,
  audioType = DEFAULT_AUDIO_TYPE,
  userId,
  email,
  createdAt = new Date(),
  speed = 1,
  bitrate,
  volume,
  demo = false,
  version: ttsVersion,
  serviceType,
  clientUserId,
  datasenses,
  isPronunciationPreview,
  projectId,
  blockId,
}) => {
  const blockedCredits = {};
  const startTime = Date.now();
  const requestId = RandomFactory.getGuid();

  const awsZoneSynthesis = MULTI_ZONE
    ? getAwsZoneSynthesis() || DEFAULT_AWS_REGION
    : DEFAULT_AWS_REGION;

  const isUsingProject = Boolean(projectId);

  // From Landing page
  // At this time, if isDemo is true => block (check ws.js for more details)
  const isDemo = !userId && demo;
  const synthesisType = SynthesisService.getSynthesisType({ text, sentences });
  if (synthesisType === SYNTHESIS_TYPE.MULTI_VOICE)
    text = sentences.reduce((prev, curr) => prev + curr.text.trim(), '');

  const user = await findUser({ _id: userId });
  if (!user) throw new CustomError(code.USER_NOT_FOUND);
  const studioUsageOptions = await getPackageUsageOptions({
    userId,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });

  // If feature flag is on, determine voice version can check and get voice info from voice cloning
  const { voice, ttsCoreVersion } = await getVersionVoice({
    requestType: REQUEST_TYPE.STUDIO,
    synthesisType,
    voiceCode,
    ttsVersion,
    text,
    userId,
    userFeatures: studioUsageOptions?.features,
  });

  let hasFreePreview = false; // This variable to develop the paid preview feature
  const textLength = SynthesisService.countSentenceTextLength(
    text,
    ttsCoreVersion,
  );

  const useEmphasisV2 = getFeatureValue(FEATURE_KEYS.EMPHASIS_FEATURE_V2, {
    userId,
    voiceCode,
  });

  if (ttsCoreVersion === TTS_CORE_VERSION.NEW && !useEmphasisV2) {
    text = text.replace(
      REGEX.OLD_BREAK_TIME,
      (p1, p2) => `${`<break time="${p2}s"/>`}`,
    );
  }

  const isEmphasisVoice = voice?.styles && voice?.styles?.includes('emphasis');

  if (isEmphasisVoice && useEmphasisV2)
    text = text.replace(
      REGEX.NEW_BREAK_TIME,
      (p1, p2) => `${`<break time=${p2}s/>`}`,
    );

  if (isDemo && textLength > TEXT_DEMO_MAX_LENGTH)
    throw new CustomError(code.TEXT_DEMO_TOO_LONG);

  // TODO: refactor

  if (!user.firstConvertAt && !isDemo) {
    updateUserById(userId, { firstConvertAt: new Date() }).catch((error) => {
      logger.error(error, { ctx: 'UpdateFirstConvertAt' });
    });
  }

  const {
    packageExpiryDate,
    remainingCharacters,
    bonusCharacters,
    packageCode,
  } = user;

  const {
    retentionPeriod,
    concurrentRequest,
    features,
    maxLengthInputText,
    maxLengthDemoInput,
  } = studioUsageOptions;

  if (!packageCode) throw new CustomError(code.PACKAGE_NOT_EXIST);
  if (demo && !features?.includes(PACKAGE_FEATURE.PREVIEW))
    throw new CustomError(code.LIMITED_FEATURE);

  if (moment().isAfter(packageExpiryDate, 'day'))
    throw new CustomError(code.PACKAGE_EXPIRED);

  // Validate number of requests zero shot with free user
  const isFreeUser = checkFreePackage(packageCode);
  const isInstantVoice = voice?.type === VOICE_TYPE.ZERO_SHOT;

  if (isFreeUser && isInstantVoice) {
    const { maxInstantVoiceSynthesis } = studioUsageOptions;
    const { usedFreeInstantVoiceSynthesis } = user;
    if (usedFreeInstantVoiceSynthesis >= maxInstantVoiceSynthesis)
      throw new CustomError(code.LIMIT_FREE_INSTANT_VOICE_SYNTHESIS);

    await increaseUsedFreeInstantVoiceSynthesis(userId);
  }

  const isSpamAccount = await checkSpamAccount({
    email,
    ip,
    packageCode,
    userId,
    feature: demo ? PACKAGE_FEATURE.PREVIEW : PACKAGE_FEATURE.TTS,
  });
  if (isSpamAccount) {
    logger.info('Check spam account in synthesis', {
      email,
      ip,
      packageCode,
      userId,
      feature: demo ? PACKAGE_FEATURE.PREVIEW : PACKAGE_FEATURE.TTS,
      ctx: 'CheckSpamAccount',
    });
    throw new CustomError(code.SPAM_ACCOUNT);
  }

  // Rate limit tts with free user or preview
  if ((isFreeUser && !demo && !isUsingProject) || demo) {
    const feature = demo ? PACKAGE_FEATURE.PREVIEW : PACKAGE_FEATURE.TTS;
    const rateLimitConfig =
      getFeatureValue(FEATURE_KEYS.RATE_LIMIT_FEATURE) || {};
    const rateLimitService = new RateLimitService(rateLimitConfig);
    const isExceedRateLimit = await rateLimitService.isExceed({
      userId,
      feature,
      characters: textLength,
    });

    if (isExceedRateLimit) {
      const isBanned = rateLimitService.isBanned(userId, feature);
      const shouldBanAccount = isFreeUser || feature !== PACKAGE_FEATURE.TTS;

      if (shouldBanAccount) {
        const banReason = rateLimitService.getBannedReason(feature);
        await BannedAccountService.add({
          type: RATE_LIMIT_TYPE.USER_ID,
          value: userId,
          feature,
          banReason,
          enabled: isBanned,
        });
      }

      if (isBanned) {
        throw new CustomError(code.SPAM_ACCOUNT);
      }
    }
  }

  if (demo) hasFreePreview = true;

  const useUserEolDate = getFeatureValue(
    FEATURE_KEYS.UPDATE_VOICES_BY_USER_EOL_DATE,
    { userId },
  );

  const configStorage = getFeatureValue(FEATURE_KEYS.CONFIG_STORAGE_TTS, {
    userId,
    ip,
  });

  const { bucket: gcsBucket = {} } = configStorage?.gcs || {};

  const sentencesCreditsInfo = await countCreditsByVoiceFactor({
    sentences: sentences.length ? sentences : [{ text, voiceCode }],
    ssmlRegex:
      ttsCoreVersion === TTS_CORE_VERSION.NEW ? REGEX.ADVANCE_TAG : undefined,
    user,
  });

  validateWalletCredits({
    demo,
    sentencesCreditsInfo,
    currWallets: {
      onetimeCredits: remainingCharacters + bonusCharacters,
      cycleCredits: user?.studio?.cycle?.remainingCredits || 0,
      topUpCredits: user?.studio?.topUp?.remainingCredits || 0,
      customCredits: user?.studio?.custom?.remainingCredits || 0,
    },
  });

  const credits = sentencesCreditsInfo.reduce(
    (acc, curr) => acc + curr.credits,
    0,
  );

  const awsZone = (await getAwsZone({ region: awsZoneSynthesis })) || {};
  const {
    normalizerFunction = DEFAULT_NORMALIZER_FUNCTION,
    sentenceTokenizerFunction = DEFAULT_SENTENCE_TOKENIZER_FUNCTION,
    newSentenceTokenizerFunction = DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION,
    textToAllophoneFunction = DEFAULT_T2A_FUNCTION,
    synthesisFunction = DEFAULT_SYNTHESIS_FUNCTION,
    joinSentencesFunction = DEFAULT_JOIN_AUDIO_FUNCTION,
    defaultS3Bucket = DEFAULT_BUCKET_S3,
    s3Buckets = {},
  } = awsZone;

  const { paragraphBreak, sentenceBreak, majorBreak, mediumBreak } =
    await getClientPause(clientPause, userId);

  const pronunciationDict = await findDictionary(userId);
  const usedWordsInPronunciationDict = dictionaryService.findUsedWords(
    text,
    pronunciationDict,
  );

  if (synthesisType === SYNTHESIS_TYPE.MULTI_VOICE) {
    let isBlockOneTimeCredits = false;
    let isUseInstantVoice = false;
    sentences = await Promise.all(
      sentences.map(async (sentence, index) => {
        const sentenceVoice = await getVoiceInfoByCode(
          sentence.voiceCode,
          userId,
          studioUsageOptions.features,
        );
        if (!sentenceVoice) throw new CustomError(code.INVALID_VOICE_CODE);

        const useLockOneTimeCredits = shouldLockOneTimeCredits({
          user,
          voice: sentenceVoice,
        });

        if (isAdvancedGlobalVoice(sentenceVoice) && useLockOneTimeCredits)
          isBlockOneTimeCredits = true;

        if (sentenceVoice?.active === false)
          throw new CustomError(code.INACTIVE_VOICE);

        if (sentenceVoice.version === TTS_CORE_VERSION.NEW)
          throw new CustomError(code.SENTENCES_NOT_SUPPORT_EMPHASIS);

        if (
          !isDemo &&
          !checkVoicePermission(features, sentenceVoice.features, user)
        )
          throw new CustomError(code.UNAVAILABLE_VOICE);

        if (!isUseInstantVoice && sentenceVoice?.type === VOICE_TYPE.ZERO_SHOT)
          isUseInstantVoice = true;

        const isEolVoice = sentenceVoice?.eolDate;
        if (
          isEolVoice &&
          useUserEolDate &&
          !canUseEOLVoice({ voice: sentenceVoice, user })
        )
          throw new CustomError(code.NO_MORE_SUPPORT_VOICE);

        const isValidText = validateText({
          text: sentence.text,
          voiceProvider: sentenceVoice.provider,
          ttsCoreVersion,
        });
        if (!isValidText) throw new CustomError(code.INVALID_SYNTAX);

        const sentenceCharacters = SynthesisService.countSentenceTextLength(
          sentence.text,
          ttsCoreVersion,
        );

        const sentenceCredits = sentencesCreditsInfo[index]?.credits;

        const isSentenceMonetizable = checkMonetizable({
          type: REQUEST_TYPE.STUDIO,
          packageCode,
          voice: sentenceVoice,
          demo,
        });

        return {
          ...sentence,
          credits: sentenceCredits || sentenceCharacters,
          characters: sentenceCharacters,
          isMonetizable: isSentenceMonetizable,
        };
      }),
    );

    if (isUseInstantVoice && isFreeUser) {
      const { maxInstantVoiceSynthesis } = studioUsageOptions;
      const { usedFreeInstantVoiceSynthesis } = user;
      if (usedFreeInstantVoiceSynthesis >= maxInstantVoiceSynthesis)
        throw new CustomError(code.LIMIT_FREE_INSTANT_VOICE_SYNTHESIS);

      await increaseUsedFreeInstantVoiceSynthesis(userId);
    }

    if (isBlockOneTimeCredits) {
      blockedCredits.oneTimeCredits = true;

      validateCredits({
        demo,
        maxLengthDemoInput,
        maxLengthInputText,
        oneTimeCredits: remainingCharacters + bonusCharacters,
        cycleCredits: user?.studio?.cycle?.remainingCredits,
        topUpCredits: user?.studio?.topUp?.remainingCredits,
        customCredits: user?.studio?.custom?.remainingCredits,
        credits,
        textLength,
        blockedCredits,
      });
    }

    const firstVoice = await getVoiceInfoByCode(
      sentences[0].voiceCode,
      userId,
      studioUsageOptions?.features,
    );

    const isMonetizable = sentences.some((sentence) => sentence.isMonetizable);

    /** the request object in memory and saved to DB, contains all info of the request, with validation and decoration (packageCode, backgroundMusic, userId, sampleRate) */
    let request = {
      ip,
      device,
      deviceInfo,
      requestId,
      title: title || getTitle(text, firstVoice.languageCode),
      sentences,
      characters: textLength,
      credits,
      audioType,
      createdAt,
      status: REQUEST_STATUS.IN_PROGRESS,
      voiceCode: sentences[0].voiceCode,
      bitrate: bitrate || DEFAULT_BITRATE,
      volume,
      retentionPeriod,
      type: REQUEST_TYPE.STUDIO,
      pronunciationDict: usedWordsInPronunciationDict,
      clientPause: {
        paragraphBreak: paragraphBreak || DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
        sentenceBreak: sentenceBreak || DEFAULT_CLIENT_PAUSE.SENTENCE_BREAK,
        majorBreak: majorBreak || DEFAULT_CLIENT_PAUSE.MAJOR_BREAK,
        mediumBreak: mediumBreak || DEFAULT_CLIENT_PAUSE.MEDIUM_BREAK,
      },
      version: ttsCoreVersion,
      serviceType: serviceType || SERVICE_TYPE.AI_VOICE,
      clientUserId,
      awsZoneSynthesis,
      awsZoneFunctions: {
        normalizerFunction,
        sentenceTokenizerFunction,
        newSentenceTokenizerFunction,
        textToAllophoneFunction,
        synthesisFunction,
        joinSentencesFunction,
        s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
      },
      googleCloudStorage: {
        bucket: gcsBucket[retentionPeriod],
      },
      datasenses,
      isMonetizable,
    };
    if (packageCode) request.packageCode = packageCode;
    if (backgroundMusic) {
      request.backgroundMusic = backgroundMusic;
    }
    if (userId) request = { ...request, userId };

    const voiceCodes = sentences.reduce((acc, curr) => {
      if (curr.voiceCode && !acc.includes(curr.voiceCode))
        return [...acc, curr.voiceCode];
      return acc;
    }, []);
    const { maxSampleRate } = await getValidSampleRates(
      voiceCodes,
      userId,
      studioUsageOptions?.features,
    );
    if (!maxSampleRate)
      throw new CustomError(
        code.BAD_REQUEST,
        'Sentences with voices that are not combined',
      );
    request.sampleRate = maxSampleRate.toString();
    await createRequest(request);

    const cacheSentences = sentences.map((sentence) => {
      const { text: textSentence, ...cacheSentence } = sentence;
      return cacheSentence;
    });

    /** it is the request object (already saved in DB) plus "sentences", "voice", progress, sentenceKeys, numberOfIndexSentences, numberOfSentences ==> save to Cache */
    const cacheRequest = { ...request, sentences: cacheSentences };
    cacheRequest.voice = firstVoice;
    cacheRequest.progress = 0;
    const sentenceKeys = Array.from(Array(sentences.length).keys()).map(
      (index) => `${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${index}`,
    );
    cacheRequest.sentenceKeys = sentenceKeys;
    cacheRequest.numberOfIndexSentences = sentences.length;
    cacheRequest.numberOfSentences = sentences.length;
    // ADVISE: duplicated code in this cache request into Redis (apiSynthensis)
    await RequestCaching.createRequestInRedis(cacheRequest);

    ProcessingTimeCaching.saveStepProcessingTime({
      requestId,
      step: TTS_PROCESSING_STEPS.PRE_PROCESSING,
      startTime,
      additionalFields: {
        userId,
        characters: textLength,
        startTime,
        pushToQueueAt: Date.now(),
      },
    });

    // queue the requestId (for current userId, ccr)
    const processRequestStatus = await enqueueRequestAndTryToConsume({
      userId,
      requestId,
      ccr: concurrentRequest,
      requestType: REQUEST_TYPE.STUDIO,
    });
    if (!processRequestStatus) {
      // ADVISE: business: cachingRequest has existed in Redis, request existed in DB
      throw new CustomError(
        code.INTERNAL_SERVER_ERROR,
        'Cannot push request to queue',
      );
    }

    // ADVISE: duplicated code in this same function
    if (userId && !hasFreePreview) {
      spendCharacters({
        userId,
        requestId,
        characters: credits || textLength,
        blockedCredits,
        sentencesCreditsInfo,
      });
      sendMessage(KAFKA_TOPIC.TTS_CREATED, {
        value: {
          requestId,
          userId,
          characters: credits || textLength,
          blockedCredits,
          sentencesCreditsInfo,
        },
      });
    }

    const sentenceVoice = await getVoiceInfoByCode(
      sentences[0].voiceCode,
      userId,
      studioUsageOptions.features,
    );

    // Don't use await so it wouldn't affect the response time
    MoEngageAdapter.sendEventCreateRequest({ customerId: userId, request });

    request.voice = sentenceVoice;
    delete request.voiceCode;
    delete request.awsZoneFunctions;
    delete request.awsZoneSynthesis;
    delete request.datasenses;
    delete request.deviceInfo;
    return request;
  } // multi_voice case, return immediatelly when finish the case

  if (!checkVoicePermission(features, voice.features, user))
    throw new CustomError(code.UNAVAILABLE_VOICE);

  const isEolVoice = voice?.eolDate;
  if (isEolVoice && useUserEolDate && !canUseEOLVoice({ voice, user }))
    throw new CustomError(code.NO_MORE_SUPPORT_VOICE);

  const isValidText = validateText({
    text,
    voiceProvider: voice.provider,
    ttsCoreVersion,
  });
  if (!isValidText) throw new CustomError(code.INVALID_SYNTAX);

  /** the request object in memory and saved to DB, contains all info of the request, with validation and decoration
   * (packageCode, backgroundMusic, userId, sampleRate, projectId, synthesisComputePlatform, isPriority) */
  let request = {
    ip,
    device,
    deviceInfo,
    requestId,
    title: title || getTitle(text, voice.languageCode),
    text,
    paragraphs,
    characters: textLength,
    credits,
    audioType,
    speed,
    createdAt,
    status: REQUEST_STATUS.IN_PROGRESS,
    voiceCode,
    bitrate: bitrate || DEFAULT_BITRATE,
    sampleRate: voice.defaultSampleRate.toString(),
    volume,
    demo,
    isPronunciationPreview,
    retentionPeriod,
    pronunciationDict: usedWordsInPronunciationDict,
    clientPause: {
      paragraphBreak: paragraphBreak || DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
      sentenceBreak: sentenceBreak || DEFAULT_CLIENT_PAUSE.SENTENCE_BREAK,
      majorBreak: majorBreak || DEFAULT_CLIENT_PAUSE.MAJOR_BREAK,
      mediumBreak: mediumBreak || DEFAULT_CLIENT_PAUSE.MEDIUM_BREAK,
    },
    type: REQUEST_TYPE.STUDIO,
    version: ttsCoreVersion,
    serviceType: serviceType || SERVICE_TYPE.AI_VOICE,
    clientUserId,
    awsZoneSynthesis,
    awsZoneFunctions: {
      normalizerFunction,
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      textToAllophoneFunction,
      synthesisFunction,
      joinSentencesFunction,
      s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
    },
    googleCloudStorage: {
      bucket: gcsBucket[retentionPeriod],
    },
    datasenses,
  };
  if (packageCode) request.packageCode = packageCode;
  if (backgroundMusic) {
    request.backgroundMusic = backgroundMusic;
  }
  if (userId) request = { ...request, userId };
  if (isUsingProject) {
    const project = await findProject({ userId, projectId });
    if (!project) throw new CustomError(code.BAD_REQUEST, 'Project not found!');
    request = { ...request, projectId, sampleRate: project.sampleRate };
  }

  request.synthesisComputePlatform = getSynthesisComputePlatform(request);

  // Prioritize request if it valid condition priority request and turn on synthesis by GPU
  const isPriorityRequest = getFeatureValue(
    FEATURE_KEYS.PROCESS_PRIORITIZE_REQUEST,
    {
      userId: request.userId,
      email,
      voiceCode: request.voiceCode,
      audioType: request.audioType,
      voice: voice.provider,
      characters: textLength,
      demo: request.demo,
    },
  );
  if (isPriorityRequest) request.isPriority = true;

  const isMonetizable = checkMonetizable({
    type: REQUEST_TYPE.STUDIO,
    packageCode,
    voice,
    demo,
  });
  request.isMonetizable = isMonetizable;

  await createRequest(request);
  if (isUsingProject) {
    await updateBlockRequestId({
      projectId,
      blockId,
      requestId,
    });
  }

  const { text: textRequest, ...cacheRequest } = request;
  cacheRequest.numberOfSentences = 0;
  cacheRequest.voice = voice;
  cacheRequest.progress = 0;
  cacheRequest.sentenceKeys = [
    `${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${0}`,
  ];
  cacheRequest.numberOfIndexSentences = 1;
  await RequestCaching.createRequestInRedis(cacheRequest);

  ProcessingTimeCaching.saveStepProcessingTime({
    requestId,
    startTime,
    step: TTS_PROCESSING_STEPS.PRE_PROCESSING,
    additionalFields: {
      userId,
      startTime,
      characters: textLength,
      pushToQueueAt: Date.now(),
    },
  });

  // TODO: limit preview tts by ip
  if (demo || hasFreePreview) {
    callApiSynthesis(requestId);
  } else {
    const processRequestStatus = await enqueueRequestAndTryToConsume({
      userId,
      requestId,
      ccr: concurrentRequest,
      requestType: REQUEST_TYPE.STUDIO,
    });
    if (!processRequestStatus)
      throw new CustomError(
        code.INTERNAL_SERVER_ERROR,
        'Cannot push request to queue',
      );
  }

  // ADVISE: duplicated code in this same function
  if (userId && !hasFreePreview) {
    spendCharacters({
      userId,
      requestId,
      characters: credits || textLength,
      blockedCredits,
      sentencesCreditsInfo,
    });
    sendMessage(KAFKA_TOPIC.TTS_CREATED, {
      value: {
        requestId,
        userId,
        characters: credits || textLength,
        blockedCredits,
        sentencesCreditsInfo,
      },
    });
  }
  // Don't use await so it wouldn't affect the response time
  MoEngageAdapter.sendEventCreateRequest({ customerId: userId, request });

  delete request.voiceCode;
  request.voice = voice;
  request.progress = 0;
  request.blockId = blockId;
  delete request.awsZoneFunctions;
  delete request.awsZoneSynthesis;
  delete request.datasenses;
  delete request.deviceInfo;
  return request;
};

module.exports = {
  handleSynthesisRequest,
};
