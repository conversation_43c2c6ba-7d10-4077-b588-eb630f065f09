require('dotenv').config();
const { initMongoDB } = require('../../models');
const VoiceCloningSampleScript = require('../../models/voiceCloningSampleScript');
const logger = require('../../utils/logger');

global.logger = logger;

(async () => {
  await initMongoDB();
  logger.info(`Starting update voice cloning sample script...`, {
    ctx: 'RunScriptUpdateVoiceCloningSampleScript',
  });

  const voiceCloningSampleScripts = require('./seedVoiceCloningSampleScript.json');

  for (const voiceCloningSampleScript of voiceCloningSampleScripts) {
    await VoiceCloningSampleScript.findOneAndUpdate(
      { category: voiceCloningSampleScript.category },
      voiceCloningSampleScript,
      { upsert: true },
    );
  }

  logger.info(`Update voice cloning sample script successfully`, {
    ctx: 'RunScriptUpdateVoiceCloningSampleScript',
  });
  process.exit(1);
})();
