const moment = require('moment');
const {
  LOADING_SYNTHESIS_FACTOR,
  ESTIMATED_TIME_PER_ONE_THOUSAND_CHARACTERS,
} = require('../constants');

const RequestCaching = require('../caching/requestCaching');

const requestDao = require('../daos/request');

/** pure function for calc */
const calculateEstimateProgress = (processTime, characters) => {
  const timeProcessEstimate =
    (characters / 1000) * ESTIMATED_TIME_PER_ONE_THOUSAND_CHARACTERS;

  let progress = processTime / timeProcessEstimate;

  progress = (Math.round(progress * 100) / 100).toFixed(2);
  return Math.min(progress, LOADING_SYNTHESIS_FACTOR.MAX_PROCESSING);
};

const getProgress = async (requestId) => {
  let request;
  request = await RequestCaching.findRequestByIdInRedis(requestId);
  const isRequestNotFound = Object.keys(request).length === 0;
  if (isRequestNotFound) request = await requestDao.findRequestById(requestId);

  const { _id, status, audioLink, characters, processingAt, endedAt } = request;
  let progress = 0;

  const processTime = moment().diff(moment(processingAt), 'seconds');
  progress = calculateEstimateProgress(processTime, characters);

  return { _id, progress, status, processingAt, endedAt, audioLink };
};

module.exports = {
  getProgress,
};
