const mongoose = require('mongoose');
const { VC_CATEGORY } = require('../constants/voiceCloning');

const voiceCloningSampleScriptSchema = new mongoose.Schema(
  {
    category: {
      type: String,
      enum: Object.values(VC_CATEGORY),
    },
    sampleScripts: [
      {
        _id: false,
        title: String,
        text: String,
      },
    ],
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

module.exports = mongoose.model(
  'VoiceCloningSampleScript',
  voiceCloningSampleScriptSchema,
  'voice_cloning_sample_script',
);
