# API version

- hiện tại coi là V4
- **IMPORTANT**: V3, <PERSON><PERSON> check Datadog, xem còn usage ko thì xoá
- tạm thời có thể ẩn route cho V3

# the /constants folder basically can be refactored to a shared `models` repository

to migrate safely, we import the `models` repository here, reassign the const with value from `models`

after a period of monitoring, we remove constants here (when everything is refer to `models` repository, not to this constants)

Should export an object, to be namespaced the constants, improve readability

# update deps

## update axios

## update jsonwebtoken similar to tts-api

## Use package-lock

# Should we cache Voice Dto?

- ít thay đổi

  - Với Giọng của bên thứ 3, Microsoft, poly
  - Với giọng Built-in VBee

- Hay thay đổi
  - Riêng với VC, sự thay đổi diễn ra thường xuyên hơn do user (chủ giọng) c<PERSON> nhu cầu thay đổi tên tuổi, ảnh demo ...
  - vẫn c<PERSON> thể cache, nhưng lưu ý refresh cache khi có sự thay đổi

# cyclic references, MOST TROUBLE PROBLEMS to refactor

1. services/voiceCloning.js > services/tts.js
2. services/queue.js > services/ttsProcessing.js
3. services/queue.js > services/ttsProcessing.js > services/ws.js > services/synthesis.js
4. services/ttsProcessing.js > services/ws.js > services/synthesis.js

---

- services/ttsProcessing

  - ./ws // ADVISE: Circular deps (inline require./synthesis)
  - ./dubbing // ADVISE: Circular deps (services/dubbing.js > services/request.js > services/ttsProcessing.js)
  - ./queue
    // ADVISE: Circular deps (services/queue.js> inline ttsProcessing.js/callApiSynthesis)

  - ./characterProcessing

  - ./secondProcessing
  - ./package
  - ./synthesisComputePlatform
  - ./voiceCloning
  - ./audio

---

- queue
  - inline ttsProcessing.js/callApiSynthesis

---

- services/ws

  - services/synthesis.js/handleSynthesisRequest() (INLINE call)
  - ./SynthesisService
  - ./recaptcha
  - ./package

---

- services/synthesis.js
  - services/ttsProcessing.js
  - services/cache.js
  - services/queue.js // ADVISE: potential cyclie
  - services/dubbing.js // ADVISE: potential cyclie
  - services/processingTime.js
  - services/characterProcessing.js
  - services/preprocessing.js

## commit 9eb337c - about call loop in queue service

Đoạn này nó gây loop như sau
processQueueWhenRequestSuccess() hoặc processQueueWhenRequestFailure()
đều call decrPendAndInprRequests()

trong trường hợp request?.status === REQUEST_STATUS.FAILURE

Thì nó sẽ loop call mãi decrPendAndInprRequests() ==> runSentenceTokenizerQueue() ==> decrPendAndInprRequests()

> giải thích nghiệp vụ là: decrease count, lấy job tiếp theo của cùng userid để xử lý, nếu đã failed (status = failure) ==> lấy job tiếp

> A nghĩ decrPendAndInprRequests() call runSentenceTokenizerQueue() là không hợp lý, có thể phải refactor tách rời 2 việc này ra khỏi nhau, decrease riêng và take next job riêng

## Remove from the Consumer entry points

DELETED: SYNTHESIS_SUCCESS

- TODO: migrateTtsFromRedisToDB
- TODO: handleStreamAudio

- TODO: handleTtsSuccess

DELETED: SYNTHESIS_FAILURE

- TODO: migrateTtsFromRedisToDB
- TODO: saveProcessingTime
- TODO: deleteInProgressRequest
- TODO: handleTtsFailure (exists in tts-api, should be deleted?)
- TODO: updateFinalRequestFromRedisToDB (exists in tts-api, should be deleted?)

- TODO: handleJoinAudiosSuccessResponse (still have references, exists in tts-api, should be delete?)
- TODO: handleJoinAudiosFailureResponse (still have references, exists in tts-api, should be delete?)

## orphans

- TODO: adapters/account.js
- TODO: daos/v3Ip.js
- TODO: services/datasenses.js

## REQUEST_STATUS

// ADVISE: compose getRequestStatusFromRedis(), Find all occurences of hardcode "REQUEST*STATUS*", extract the business logic to a separate func.
// #region getRequestStatusFromRedis

// ADVISE: REQUEST_STATUS is old process, new process which curently inused will be prefixed with AIVOICE\_

# Knowledge

## ngay khi tạo được TTSRequest thì API lưu đồng thời vào Mongo và Redis

Trong các quá trình trung gian, truy cập từ Redis cho tốc độ nhanh hơn

// ADVISE: BUSINESS: explain caching mechanism of request and sentences
const cacheSentences = sentences.map((sentence) => {
// ADVISE: textSentence is unused?
const { text: textSentence, ...cacheSentence } = sentence;
return cacheSentence;
});
