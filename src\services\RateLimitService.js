const { REDIS_KEY_PREFIX, PACKAGE_FEATURE } = require('../constants');
const { RATE_LIMIT_TYPE } = require('../constants/rateLimiter');
const BannedAccountService = require('./BannedAccountService');
const Caching = require('../caching');

class RateLimitService {
  constructor(rateLimitConfig) {
    this.rateLimitConfig = rateLimitConfig || {};
  }

  static getCacheKey(userId, feature) {
    if (feature === PACKAGE_FEATURE.PREVIEW)
      return `${REDIS_KEY_PREFIX.PREVIEW_TTS_RATE_LIMIT}:${userId}`;

    return `${REDIS_KEY_PREFIX.TTS_RATE_LIMIT}:${userId}`;
  }

  static getTtl(rateLimitFeatureConfig) {
    return rateLimitFeatureConfig?.ttl;
  }

  static get(rateLimitFeatureConfig) {
    return rateLimitFeatureConfig?.limit;
  }

  getBannedReason(feature) {
    if (feature === PACKAGE_FEATURE.PREVIEW)
      return `User exceeded preview rate limit`;

    const rateLimitFeatureConfig = this.rateLimitConfig[feature];
    return `User exceeded ${feature} rate limit ${rateLimitFeatureConfig?.limit} times in ${rateLimitFeatureConfig?.ttl}s`;
  }

  // Calculate time to wait for the next request
  calculateTimeToWait(feature, characters) {
    const rateLimitFeatureConfig = this.rateLimitConfig[feature];
    const defaultTtl = RateLimitService.getTtl(rateLimitFeatureConfig);

    let ttl;
    if (feature === PACKAGE_FEATURE.PREVIEW) {
      // const AVG_SYNTHESIS_DURATION = 10; // Average synthesis duration in seconds for 1000 characters
      // const AVG_AUDIO_DURATION = 50; // Average audio duration in seconds for 1000 characters
      // const AVG_DURATION = AVG_SYNTHESIS_DURATION + AVG_AUDIO_DURATION; // Average duration in seconds for 1000 characters
      const avgDurationPer1kChars = defaultTtl;
      ttl = (characters / 1000) * avgDurationPer1kChars;
      ttl = Math.ceil(ttl); // Redis ttl must be an integer
    } else {
      ttl = defaultTtl;
    }

    return ttl;
  }

  // Check exceed rate limit for tts feature
  async isExceed({ userId, feature, characters }) {
    const rateLimitFeatureConfig = this.rateLimitConfig[feature];
    if (!rateLimitFeatureConfig) return false;

    const rateLimitKey = RateLimitService.getCacheKey(userId, feature);
    const count = await Caching.GlobalCounter.increase(rateLimitKey);

    // Set ttl for the first request
    if (count === 1) {
      const ttl = this.calculateTimeToWait(feature, characters);
      await Caching.RedisRepo.expire(rateLimitKey, ttl);
    }

    // Check if exceed rate limit
    const limit = RateLimitService.get(rateLimitFeatureConfig);
    if (count > limit) return true;

    return false;
  }

  isBanned(userId, feature) {
    const DEFAULT_MAX_VIOLATIONS = 3;
    const rateLimitFeatureConfig = this.rateLimitConfig[feature];
    if (!rateLimitFeatureConfig) return false;

    const bannedAccount = BannedAccountService.find({
      type: RATE_LIMIT_TYPE.USER_ID,
      value: userId,
      feature,
    });

    if (!bannedAccount) return false;

    // Add 1 for the current request
    if (
      bannedAccount.times + 1 >=
      (rateLimitFeatureConfig.maxViolations || DEFAULT_MAX_VIOLATIONS)
    )
      return true;

    return false;
  }
}

module.exports = { RateLimitService };
