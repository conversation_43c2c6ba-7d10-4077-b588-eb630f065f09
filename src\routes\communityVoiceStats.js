const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const communityVoiceStatsController = require('../controllers/communityVoiceStats');
const { auth, hasRole } = require('../middlewares/auth');
const {
  getUserVoiceStatsValidate,
  getUserVoiceStatsByUserIdValidate,
  getCommunityVoiceStatsValidate,
} = require('../validations/communityVoiceStats');
const { S2S_ROLE } = require('../constants/roles');

/* eslint-disable prettier/prettier */
router.get('/community-voice-stats', auth, getUserVoiceStatsValidate, asyncMiddleware(communityVoiceStatsController.getUserVoiceStats));
router.get('/community-voice-stats/search', getCommunityVoiceStatsValidate, asyncMiddleware(communityVoiceStatsController.getCommunityVoiceStats));
router.get('/community-voice-stats/:userId', auth, hasRole(S2S_ROLE.MANAGE_COMMUNITY_VOICE_STATS), getUserVoiceStatsByUserIdValidate, asyncMiddleware(communityVoiceStatsController.getUserVoiceStatsByUserId));
/* eslint-disable prettier/prettier */

module.exports = router;
