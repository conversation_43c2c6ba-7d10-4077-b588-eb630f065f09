require('dotenv').config();

const { initMongoDB } = require('../../models');

initMongoDB();

const VoiceCloning = require('../../models/voiceCloning');
const logger = require('../../utils/logger');

global.logger = logger;

const data = require('./seedVoice.json');

const BATCH_SIZE = 500;

async function updateBatch(batch, batchIndex) {
  const bulkOps = batch.map(({ code, ageGroup, description, publishAt }) => ({
    updateOne: {
      filter: { code },
      update: { $set: { ageGroup, description, publishAt } },
    },
  }));

  try {
    const result = await VoiceCloning.bulkWrite(bulkOps);
    logger.info(
      `Batch ${batchIndex + 1}: Matched=${result.matchedCount}, Modified=${
        result.modifiedCount
      }`,
    );
  } catch (err) {
    logger.error(`Batch ${batchIndex + 1} failed: ${err.message}`);
  }
}

(async () => {
  try {
    for (let i = 0; i < data.length; i += BATCH_SIZE) {
      const batch = data.slice(i, i + BATCH_SIZE);
      await updateBatch(batch, i / BATCH_SIZE);
    }

    logger.info('All voice cloning updates completed');
    process.exit(0);
  } catch (error) {
    logger.error(`Error during update: ${error.message}`);
    process.exit(1);
  }
})();
