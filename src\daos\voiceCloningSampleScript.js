const VoiceCloningSampleScript = require('../models/voiceCloningSampleScript');

const getVoiceCloningSampleScript = async (category) => {
  const script = await VoiceCloningSampleScript.findOne({ category }).lean();
  return script;
};

const getVoiceCloningSampleScripts = async () => {
  const scripts = await VoiceCloningSampleScript.find({}).lean();
  return scripts;
};

module.exports = { getVoiceCloningSampleScript, getVoiceCloningSampleScripts };
