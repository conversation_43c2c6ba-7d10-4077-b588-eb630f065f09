const VoiceCloning = require('../models/voiceCloning');
const daoUtils = require('./utils');
const { findLanguages } = require('./language');
const { VOICE_OWNERSHIP, VOICE_SCOPE } = require('../constants/voice');
const {
  VOICE_STATUS,
  VOICE_CREATION_MODE,
} = require('../constants/voiceCloning');
const { VOICE_CLONING_TYPE } = require('../constants');

const buildArrayFilter = (value) => {
  return value ? { $in: value.split(',') } : undefined;
};

const buildQueryField = (baseQuery, dataQuery) => {
  if (!dataQuery) return baseQuery;

  const {
    gender,
    language,
    region,
    category,
    ageGroup,
    voiceCloningType,
    scope,
    ...otherFields
  } = dataQuery;

  const queryField = {
    ...baseQuery,
    ...otherFields,
  };

  if (gender) queryField.gender = buildArrayFilter(gender);
  if (ageGroup) queryField.ageGroup = buildArrayFilter(ageGroup);
  if (category) queryField.category = buildArrayFilter(category);
  if (voiceCloningType) {
    const voiceCloningTypeArray = voiceCloningType.split(',');
    const hasInstant = voiceCloningTypeArray.includes(
      VOICE_CREATION_MODE.INSTANT,
    );
    const hasProfessional = voiceCloningTypeArray.includes(
      VOICE_CREATION_MODE.PROFESSIONAL,
    );
    if (hasInstant && hasProfessional) delete queryField.type;
    else if (hasInstant) queryField.type = VOICE_CLONING_TYPE.ZERO_SHOT;
    else if (hasProfessional)
      queryField.type = { $nin: [VOICE_CLONING_TYPE.ZERO_SHOT] };
  }
  if (scope === VOICE_SCOPE.FEATURED)
    queryField['stats.totalCredits'] = { $gte: 30000000 };
  if (language && region)
    queryField.languageCode = {
      $regex: new RegExp(`^${language}-${region}$`, 'i'),
    };
  else if (language)
    queryField.languageCode = { $regex: new RegExp(`^${language}`, 'i') };

  return queryField;
};

const createVoiceCloningVoice = async ({
  userId,
  code,
  name,
  image,
  gender,
  locale,
  province,
  status,
  languageCode,
  provider,
  squareImage,
  roundImage,
  sampleRates,
  defaultSampleRate,
  global,
  isSample,
  type,
  ttsGateCode,
  demo,
}) => {
  const voiceCloning = await VoiceCloning.updateOne(
    { code },
    {
      userId,
      code,
      name,
      image,
      gender,
      locale,
      province,
      status,
      languageCode,
      provider,
      squareImage,
      roundImage,
      sampleRates,
      defaultSampleRate,
      global,
      isSample,
      type,
      ttsGateCode,
      demo,
    },
    { upsert: true, new: true },
  );
  return voiceCloning;
};

const findVoiceCloningByCode = async (code) =>
  VoiceCloning.findOne({ code }).lean();

const findVoiceCloningByCodes = async (codes) =>
  VoiceCloning.find({ code: { $in: codes } }).lean();

const findVoiceCloningBySlug = async (slug) =>
  VoiceCloning.findOne({ 'profilePage.slug': slug }).lean();

const findVoiceCloningByIdentifier = async ({
  voiceId,
  voiceCode,
  slug,
  key,
}) => {
  if (voiceId) {
    const voice = await VoiceCloning.findById(voiceId).lean();
    if (voice) return voice;
  }

  if (voiceCode) {
    const voice = await VoiceCloning.findOne({ code: voiceCode }).lean();
    if (voice) return voice;
  }

  if (slug) {
    const voice = await VoiceCloning.findOne({
      'profilePage.slug': slug,
    }).lean();
    if (voice) return voice;
  }

  // TODO: Add more conditions
  if (key) {
    const voice = await VoiceCloning.findOne({
      $or: [{ code: key }, { 'profilePage.slug': key }],
    }).lean();
    if (voice) return voice;
  }

  return null;
};

// ADVISE: duplicated code with dao/voiceCloning Duplicate code: lines 18
const findVoiceCloningVoices = async (query = {}) => {
  const {
    search,
    searchFields = ['name'],
    query: queryField,
    offset,
    limit,
    fields,
    sort = ['rank_asc'],
  } = query;

  let dataQuery = {};
  if (queryField) {
    const { gender, languageCode, features, level, ...otherQuery } = queryField;
    dataQuery = { ...otherQuery };
    if (gender) dataQuery.gender = { $in: gender.split(',') };
    if (languageCode) dataQuery.languageCode = { $in: languageCode.split(',') };
    if (level) dataQuery.level = { $in: level.split(',') };
    if (features)
      dataQuery.features = { $elemMatch: { $in: features.split(',') } };
  }

  const { documents: voices, total } = await daoUtils.find(VoiceCloning, {
    search,
    searchFields,
    query: dataQuery,
    offset,
    limit,
    fields,
    sort,
  });

  // ADVISE: IMPORTANT: get rid of global.LANGUAGES here. For isolation, Voices must not init and setup Languages
  if (!LANGUAGES) {
    const { languages } = await findLanguages();
    global.LANGUAGES = languages;
  }

  const detailVoices = voices.map((voice) => {
    const language = LANGUAGES.find((item) => item.code === voice.languageCode);
    return { ...voice, language };
  });

  return { voices: detailVoices, total };
};

const findVoiceCloningByOwnerShip = async ({ voiceOwnership, userId }) => {
  const query =
    voiceOwnership === VOICE_OWNERSHIP.COMMUNITY
      ? {
          status: VOICE_STATUS.PUBLIC,
          $or: [
            { discardAt: { $exists: false } },
            { discardAt: { $gt: new Date() } },
          ],
        }
      : { userId, status: { $ne: VOICE_STATUS.REVIEWING } };

  const result = await findVoiceCloningVoices({ query });
  return result;
};

const findCommunityVoiceCloningForUser = async (userId) => {
  const query = {
    status: VOICE_STATUS.PUBLIC,
    $or: [
      { discardAt: { $exists: false } },
      { discardAt: { $gt: new Date() } },
    ],
    userId,
  };
  const result = await findVoiceCloningVoices({ query });
  return result;
};

const findCommunityVoices = async (query = {}) => {
  const baseQuery = {
    status: VOICE_STATUS.PUBLIC,
    $or: [
      { discardAt: { $exists: false } },
      { discardAt: { $gt: new Date() } },
    ],
  };
  const { query: dataQuery } = query;
  const queryField = buildQueryField(baseQuery, dataQuery);

  const result = await daoUtils.findWithCursor(VoiceCloning, {
    ...query,
    query: queryField,
  });

  return result;
};

const findPersonalVoices = async (userId, query = {}) => {
  const baseQuery = {
    userId,
    status: { $ne: VOICE_STATUS.REVIEWING },
  };
  const { query: dataQuery } = query;
  const queryField = buildQueryField(baseQuery, dataQuery);

  const result = await daoUtils.findWithCursor(VoiceCloning, {
    ...query,
    query: queryField,
  });

  return result;
};

const findVoiceCloningsByNameForUser = async (voiceName, userId) =>
  VoiceCloning.find({ name: { $regex: voiceName, $options: 'i' }, userId });

const updateVoiceCloningVoiceStatus = async ({ code, status }) => {
  const voice = await findVoiceCloningByCode(code);
  if (!voice) return;

  await VoiceCloning.updateOne({ code }, { status });
};

const updateClonedVoiceInfo = async (code, updateData) => {
  const updatedVoice = await VoiceCloning.findOneAndUpdate(
    { code },
    updateData,
    { new: true },
  );
  return updatedVoice;
};

const updateSampleAudioLinkById = async ({
  voiceCode,
  sampleAudioId,
  audioLink,
}) => {
  return VoiceCloning.updateOne(
    { code: voiceCode, 'sampleAudios._id': sampleAudioId },
    { $set: { 'sampleAudios.$.audioLink': audioLink } },
  );
};

const updateVoiceCloningProfilePage = async (voiceCode, updateData) => {
  await VoiceCloning.updateOne(
    { code: voiceCode },
    { $set: { profilePage: updateData } },
  );
};

const findAllCommunityVoiceCloning = async () => {
  return VoiceCloning.find({
    status: VOICE_STATUS.PUBLIC,
    $or: [
      { discardAt: { $exists: false } },
      { discardAt: { $gt: new Date() } },
    ],
  }).lean();
};

const bulkUpdateVoiceStats = async (voiceCodes, statsMap, field) => {
  const bulkOps = voiceCodes
    .map((code) => {
      const stats = statsMap[code];
      if (!stats) return null;

      return {
        updateOne: {
          filter: { code },
          update: { $set: { [field]: stats } },
          upsert: false,
        },
      };
    })
    .filter(Boolean);

  if (!bulkOps.length) {
    logger.warn('No valid stats to update', {
      field,
      voiceCodesCount: voiceCodes.length,
    });
    return;
  }

  await VoiceCloning.bulkWrite(bulkOps);
};

module.exports = {
  createVoiceCloningVoice,
  findVoiceCloningByCode,
  findVoiceCloningsByNameForUser,
  findVoiceCloningByCodes,
  findVoiceCloningVoices,
  findVoiceCloningByOwnerShip,
  updateVoiceCloningVoiceStatus,
  updateClonedVoiceInfo,
  findPersonalVoices,
  findCommunityVoices,
  findCommunityVoiceCloningForUser,
  findVoiceCloningByIdentifier,
  updateSampleAudioLinkById,
  findVoiceCloningBySlug,
  updateVoiceCloningProfilePage,
  findAllCommunityVoiceCloning,
  bulkUpdateVoiceStats,
};
