// ADVISE: what is Tts entity here?
require('dotenv').config();
const { REDIS_KEY_PREFIX } = require('../constants');
const Tts = require('../models/tts');

const logger = require('../utils/logger');

const Caching = require('../caching');
const RequestCaching = require('../caching/requestCaching');

// ADVISE: should be removed as well if we always use synthesis by gateway. The same business exist in tts-api
const migrateTtsFromRedisToDB = async (requestId) => {
  try {
    const ttsList = await RequestCaching.getTtsFromRequestIdInRedis(requestId);
    const migratedTtsKey = `${REDIS_KEY_PREFIX.MIGRATED_TTS}_${requestId}`;
    const checkMigrate = await Caching.GlobalCounter.increase(migratedTtsKey);

    if (checkMigrate > 1) return;

    await Tts.insertMany(ttsList);
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
  }
};

module.exports = {
  /** @deprecated this is business of TTS-API */
  migrateTtsFromRedisToDB,
};
