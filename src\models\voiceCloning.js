const mongoose = require('mongoose');
const {
  VOICE_GENDER,
  VOICE_LOCALE,
  VOICE_STATUS,
  LANGUAGE_CODE,
  VC_PROFILE_PAGE_STATUS,
} = require('../constants/voiceCloning');
const {
  VOICE_PROVIDER,
  VOICE_LEVEL,
  VOICE_CLONING_TYPE,
} = require('../constants');

const voiceCloningSchema = new mongoose.Schema(
  {
    active: {
      type: Boolean,
      default: true,
    },
    userId: String,
    code: String,
    ttsGateCode: String,
    name: String,
    image: String, // avatar
    gender: {
      type: String,
      enum: Object.values(VOICE_GENDER),
    },
    locale: {
      type: String,
      enum: Object.values(VOICE_LOCALE),
    },
    province: String,
    category: String,
    status: {
      type: String,
      enum: Object.values(VOICE_STATUS),
    },
    languageCode: {
      type: String,
      enum: Object.values(LANGUAGE_CODE),
      default: LANGUAGE_CODE.VI,
    },
    type: {
      type: String,
      enum: Object.values(VOICE_CLONING_TYPE),
      default: VOICE_CLONING_TYPE.FINETUNE_SCRIPTS_RECORDING,
    },
    provider: {
      type: String,
      enum: Object.values(VOICE_PROVIDER),
    },
    squareImage: String, // avatar
    roundImage: String, // avatar
    demo: String,
    rank: Number,
    features: [String],
    styles: [String], // categories
    sampleRates: [Number],
    defaultSampleRate: Number,
    synthesisFunction: String,
    global: { type: Boolean, default: false },
    level: {
      type: String,
      enum: Object.values(VOICE_LEVEL),
    },
    version: String,
    beta: Boolean,
    isSample: { type: Boolean, default: false },
    sample: {
      style: String,
      audioLink: String,
      text: String,
    },
    hasDubbing: { type: Boolean, default: false },
    retentionDays: Number,
    discardAt: Date,
    publishAt: Date,
    ageGroup: String,
    description: String,
    sampleAudios: [
      {
        title: String,
        text: String,
        audioLink: String,
      },
    ],
    profilePage: {
      status: {
        type: String,
        enum: Object.values(VC_PROFILE_PAGE_STATUS),
      },
      sampleAudioIds: [String],
      slug: { type: String, unique: true, sparse: true },
      coverImage: String,
      subCoverImage: String,
    },
    stats: {
      totalCharacters: Number,
      totalCredits: Number,
      totalMoneyUsd: Number,
      totalMoneyVnd: Number,
      totalRequests: Number,
      totalUsers: Number,
    },
    stats30d: {
      totalCharacters: Number,
      totalCredits: Number,
      totalMoneyUsd: Number,
      totalMoneyVnd: Number,
      totalRequests: Number,
      totalUsers: Number,
    },
  },
  {
    versionKey: false,
  },
);

module.exports = mongoose.model('VoiceCloning', voiceCloningSchema);
