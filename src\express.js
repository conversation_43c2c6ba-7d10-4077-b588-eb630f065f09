/* eslint-disable import/order */
const logger = require('./utils/logger');

const initHttpServer = async () => {
  return new Promise((resolve, reject) => {
    try {
      const { initSentry } = require('./sentry');
      const { PORT } = require('./configs');

      const http = require('http');
      const express = require('express');

      const cors = require('cors');
      const helmet = require('helmet');
      const compression = require('compression');

      // static middlewares (no depend on external services)
      const camelCaseReq = require('./middlewares/camelCaseReq');
      const omitReq = require('./middlewares/omitReq');
      const snakeCaseRes = require('./middlewares/snakeCaseRes');
      const successReq = require('./middlewares/successReq');
      const getDevice = require('./middlewares/getDevice');
      const getClientInfo = require('./middlewares/getClientInfo');

      // middlewares depend on external services (DB, Cache, ...)
      const errorHandler = require('./middlewares/errorHandler');
      const errorHandlerApi = require('./middlewares/errorHandlerApi');

      const app = express();

      // Global (static) Middlewares setup
      app.use(cors());
      app.use(helmet());
      app.use(compression());
      app.use(express.json({ limit: '10MB' }));
      app.use(express.urlencoded({ extended: true }));
      app.use(camelCaseReq);
      app.use(omitReq);
      app.use(snakeCaseRes());
      app.use(successReq());
      app.use(getDevice);
      app.use(getClientInfo);

      // Route registration
      require('./routes')(app);
      require('./routes/v2')(app);
      require('./routes/v3')(app);

      app.use(errorHandler);

      const apiV3Route = require('./routes/apiV3');

      app.use('/', apiV3Route);
      app.use(errorHandlerApi); // access to daos

      const server = http.createServer(app);
      const Sentry = initSentry();
      Sentry.setupExpressErrorHandler(app);
      server.listen(PORT, () => {
        logger.info(`HttpServer: Init successfully, running on port ${PORT}`, {
          ctx: 'Server.Http',
        });

        resolve(server);
      });
    } catch (error) {
      reject(error);
    }
  });
};

module.exports = { initHttpServer };
