const {
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  REQUEST_TYPE,
  REQUEST_STATUS,
} = require('../constants');

const logger = require('../utils/logger');
const requestDao = require('../daos/request');

const Caching = require('../caching');

const { updateProcessingAt } = require('./inprogressRequest');

// ============================================================================
// #region QUEUE OPERATIONS

/** pending requests queue */
const getPendingRequestsQueueKey = (userId, requestType) => {
  return requestType === REQUEST_TYPE.STUDIO ||
    requestType === REQUEST_TYPE.DUBBING
    ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_REQUESTS}_${userId}`
    : `${REDIS_KEY_PREFIX.API_PENDING_REQUESTS}_${userId}`;
};

/** Dequeue a request from the pending requests queue (pure function) */
const dequeueRequest = async (userId, requestType) => {
  const pendingReqKey = getPendingRequestsQueueKey(userId, requestType);
  const requestId = await Caching.GlobalQueue.dequeue(pendingReqKey);
  return requestId;
};

/** Enqueue a request to the pending requests queue (pure function) */
const enqueueRequest = async (userId, requestId, requestType) => {
  const queueKey = getPendingRequestsQueueKey(userId, requestType);
  const queueLength = await Caching.GlobalQueue.enqueue(queueKey, requestId);

  logger.info('Enqueued pending request to queue', {
    ctx: 'ProcessRequestByCcr',
    requestId,
    userId,
    requestType,
    queueLength,
  });

  return queueLength;
};

/** Get the size of the pending requests queue (pure function) */
const getQueueSize = async (userId, requestType) => {
  const pendingReqKey = getPendingRequestsQueueKey(userId, requestType);
  return Caching.GlobalQueue.size(pendingReqKey);
};

// #endregion
// ============================================================================

// ============================================================================
// #region COUNTER MANAGEMENT

/** pending and in-progress requests counter */
const getPendingAndInProgressCounterKey = (userId, requestType) => {
  return requestType === REQUEST_TYPE.STUDIO ||
    requestType === REQUEST_TYPE.DUBBING
    ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`
    : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;
};

/** Decrease pending and in-progress requests counter (pure function) */
const decreaseRequestCounter = async (userId, requestType) => {
  const counterKey = getPendingAndInProgressCounterKey(userId, requestType);
  const newCount = await Caching.GlobalCounter.decrease(counterKey);

  logger.info(`Decrease ${counterKey}: ${newCount}`, {
    ctx: 'ProcessRequestByCcr',
    userId,
  });

  return newCount;
};

/** Increase pending and in-progress requests counter (pure function) */
const increaseRequestCounter = async (userId, requestType) => {
  const counterKey = getPendingAndInProgressCounterKey(userId, requestType);
  const newCount = await Caching.GlobalCounter.increase(counterKey);

  return newCount;
};
// #endregion
// ============================================================================

// ADVISE: Responsibility: Dequeue request, Check request status, Handle failed requests, Call TTS processing. Problem: Mixed queue operations with business logic
/** for specific user, dequeue/pop requestId and process it */
const dequeueAndConsumeRequestWorkload = async (userId, requestType) => {
  const numOfPendingReq = await getQueueSize(userId, requestType);
  if (numOfPendingReq === 0) {
    logger.debug(`No pending request`, { ctx: 'ProcessRequestByCcr', userId });
    return;
  }

  const requestId = await dequeueRequest(userId, requestType);
  if (!requestId) {
    logger.debug(`No pending request`, { ctx: 'ProcessRequestByCcr', userId });
    return;
  }
  logger.debug('Pop pending request from queue', {
    ctx: 'ProcessRequestByCcr',
    userId,
    requestId,
  });
  await updateProcessingAt(requestId, new Date());

  const request = await requestDao.findRequestById(requestId);
  if (request?.status === REQUEST_STATUS.FAILURE) {
    logger.warn(`Request is already processed as failure`, {
      ctx: 'ProcessRequestByCcr',
      requestId,
      userId,
    });
    handleRequestCompletion(userId, request.type);
    return;
  }

  // ADVISE: should remove cyclic deps: The inline require here because "ttsProcessing" also call "queue"
  require('./ttsProcessing').callApiSynthesis(requestId);
};

/** Enqueue user's requestId into Redis Queue (to check for CCR of userId).
 * Also (try to) dequeue/pop from queue to process (if user.ccr > his allowed CCR) */
const enqueueRequestAndTryToConsume = async ({
  userId,
  requestId,
  ccr,
  requestType,
}) => {
  const userQueueLength = await enqueueRequest(userId, requestId, requestType);

  // ADVISE: separate this checkCCR/dequeue into another func

  if (userQueueLength) {
    const numOfPendAndInprReq = await increaseRequestCounter(
      userId,
      requestType,
    );
    if (ccr >= 0 && numOfPendAndInprReq > ccr) {
      logger.warn(
        `Queue ${getPendingAndInProgressCounterKey(
          userId,
          requestType,
        )} is at max CCR: ${numOfPendAndInprReq}`,
        { ctx: 'ProcessRequestByCcr', userId, requestId },
      );
      return userQueueLength;
    }

    dequeueAndConsumeRequestWorkload(userId, requestType);
  }

  return userQueueLength;
};

/** coordinates counter decrease and next job processing
 */
const handleRequestCompletion = async (userId, requestType) => {
  await decreaseRequestCounter(userId, requestType);
  await dequeueAndConsumeRequestWorkload(userId, requestType);
};

const processQueueWhenRequestFailure = async ({
  userId,
  requestId,
  requestType,
}) => {
  const failureKey = `${REDIS_KEY_PREFIX.FAILURE_REQUEST}_${requestId}`;
  const numOfFailure = await Caching.GlobalCounter.increase(failureKey);
  Caching.RedisRepo.expire(failureKey, REDIS_KEY_TTL.FAILURE_REQUEST);

  if (numOfFailure > 1) return;

  // ADVISE: CRITICAL: this cause the most headache when we want to de-cyclic reference this service. this decrPendAndInprRequests also run runSentenceTokenizerQueue then call loop to decrPendAndInprRequests
  handleRequestCompletion(userId, requestType);
};

const processQueueWhenRequestSuccess = async ({
  userId,
  requestId,
  requestType,
}) => {
  const successKey = `${REDIS_KEY_PREFIX.SUCCESS_REQUEST}_${requestId}`;
  const numOfSuccess = await Caching.GlobalCounter.increase(successKey);
  Caching.RedisRepo.expire(successKey, REDIS_KEY_TTL.SUCCESS_REQUEST);

  if (numOfSuccess > 1) return;

  if (requestType !== REQUEST_TYPE.API_CACHING) {
    // ADVISE: CRITICAL: this cause the most headache when we want to de-cyclic reference this service. this decrPendAndInprRequests also run runSentenceTokenizerQueue then call loop to decrPendAndInprRequests
    handleRequestCompletion(userId, requestType);
  }
};

module.exports = {
  enqueueRequestAndTryToConsume,

  processQueueWhenRequestFailure,
  processQueueWhenRequestSuccess,
};
