const { EnvironmentHelper } = require('@vbee-holding/vbee-node-shared-lib');
const { Constants } = require('@vbee-holding/vbee-tts-models');

// robust parsing util-functions from envvar (which is string) to int, boolean and float, unify the way we get value from envvar and fallback to default value
const { getEnvVarInt, getEnvVarFloat } = EnvironmentHelper;

const {
  PORT,
  MONGO_URI,
  REDIS_URI,
  VBEE_URL,
  VN_URL,
  KAFKA_CLIENT_ID,
  KAFKA_BROKERS,
  KAFKA_CONSUMER_GROUP_TTS,
  IAM_URL,
  IAM_REALM,
  NODE_ENV,
  ENV,
  PRODUCT,
  SERVICE,
  SERVER_ENV,
  API_TTS_VERSION_DEFAULT,
  STUDIO_TTS_VERSION_DEFAULT,
  MAX_DEMO_TTS,
  MAX_DEMO_TTS_TTL,
  SYNC_ENTERPRISE_SERVICE_URLS,
} = process.env;

module.exports = {
  PORT: PORT || 80,
  MONGO_URI,
  REDIS_URI,
  VBEE_URL,
  VN_URL,
  KAFKA_CLIENT_ID: KAFKA_CLIENT_ID || 'kafka-client',
  NODE_ENV: NODE_ENV || 'dev',
  ENV: ENV || 'dev',
  PRODUCT: PRODUCT || 'vbee',
  SERVICE: SERVICE || 'vbee-tts-api',
  SERVER_ENV,
  API_TTS_VERSION_DEFAULT,
  STUDIO_TTS_VERSION_DEFAULT,

  S3_ACCESS_KEY_ID: process.env.S3_ACCESS_KEY_ID,
  S3_SECRET_ACCESS_KEY: process.env.S3_SECRET_ACCESS_KEY,
  S3_BUCKET: {
    VOICE_CLONING: process.env.S3_BUCKET_VOICE_CLONING,
  },

  GCS_AICORE_TTS_CLIENT_EMAIL: process.env.GCS_AICORE_TTS_CLIENT_EMAIL,
  GCS_AICORE_TTS_PRIVATE_KEY: process.env.GCS_AICORE_TTS_PRIVATE_KEY,
  GCS_BUCKET: {
    AI_VOICE: process.env.GCS_BUCKET_AI_VOICE,
  },

  PUBLIC_RSA_KEY_ENCRYPT_ACCESS_KEY:
    process.env.PUBLIC_RSA_KEY_ENCRYPT_ACCESS_KEY,
  PRIVATE_RSA_KEY_ENCRYPT_ACCESS_KEY:
    process.env.PRIVATE_RSA_KEY_ENCRYPT_ACCESS_KEY,

  KAFKA_BROKERS: KAFKA_BROKERS?.split(',') || [],
  KAFKA_CONSUMER_GROUP_TTS: KAFKA_CONSUMER_GROUP_TTS || 'tts',

  IAM_URL,
  IAM_REALM,
  IAM_CLIENT_ID: process.env.IAM_CLIENT_ID || 'vbee-tts-api',
  IAM_CLIENT_SECRET: process.env.IAM_CLIENT_SECRET,
  IAM_VALID_CLIENT_IDS: process.env.IAM_VALID_CLIENT_IDS?.split(',') || [],
  MAX_DEMO_TTS: MAX_DEMO_TTS || 3,
  MAX_DEMO_TTS_TTL: MAX_DEMO_TTS_TTL || 24, // hours
  MAX_PREVIEW_TTS: getEnvVarInt('MAX_PREVIEW_TTS', 300),
  MAX_PREVIEW_TTS_TTL: parseInt('MAX_PREVIEW_TTS_TTL', 24),
  MAX_DEMO_TTS_LENGTH: getEnvVarInt('MAX_DEMO_TTS_LENGTH', 500),
  MAX_PREVIEW_TTS_LENGTH: getEnvVarInt('MAX_PREVIEW_TTS_LENGTH', 2000),

  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
  AWS_REGION: process.env.AWS_REGION,

  NORMALIZE_FUNCTION_NAME: process.env.NORMALIZE_FUNCTION_NAME,
  ERROR_REPORT_SLACK_CHANNEL: process.env.ERROR_REPORT_SLACK_CHANNEL,
  NOTIFICATION_URL: process.env.NOTIFICATION_URL,
  SILENCE_BREAK_LINE_AUDIO: process.env.SILENCE_BREAK_LINE_AUDIO,
  TTS_CLIENT_ID: 'vbee-tts-api',

  ACCOUNT_URL: process.env.ACCOUNT_URL,
  CONSOLE_URL: process.env.CONSOLE_URL,
  BACK_OFFICE_URL: process.env.BACK_OFFICE_URL,
  UPLOAD_URL: process.env.UPLOAD_URL,
  TTS_API_URL: process.env.TTS_API_URL,
  VOICE_CLONING_URL: process.env.VOICE_CLONING_URL,

  PRIVATE_RSA_KEY: process.env.PRIVATE_RSA_KEY,
  SYNC_ENTERPRISE_SERVICE_URLS: SYNC_ENTERPRISE_SERVICE_URLS?.split(',') || [],
  DEFAULT_AWS_REGION: process.env.DEFAULT_AWS_REGION,
  DEFAULT_NORMALIZER_FUNCTION: process.env.DEFAULT_NORMALIZER_FUNCTION,
  DEFAULT_SENTENCE_TOKENIZER_FUNCTION:
    process.env.DEFAULT_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION:
    process.env.DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_T2A_FUNCTION: process.env.DEFAULT_T2A_FUNCTION,
  DEFAULT_SYNTHESIS_FUNCTION: process.env.DEFAULT_SYNTHESIS_FUNCTION,
  DEFAULT_JOIN_AUDIO_FUNCTION: process.env.DEFAULT_JOIN_AUDIO_FUNCTION,
  DEFAULT_BUCKET_S3: process.env.DEFAULT_BUCKET_S3,
  DEFAULT_RETENTION_PERIOD: getEnvVarInt('DEFAULT_RETENTION_PERIOD', 3),
  MULTI_ZONE: getEnvVarInt('MULTI_ZONE', 0),
  TEXT_DEMO_MAX_LENGTH: getEnvVarInt('TEXT_DEMO_MAX_LENGTH', 200),
  DEFAULT_AUDIO_TYPE: Constants.AUDIO_TYPE.MP3,
  AUDIO_URL_EXPIRES: 60 * 3, // 3 minutes
  API_CHARACTERS_RUN_OUT_NUMBER: getEnvVarInt(
    'API_CHARACTERS_RUN_OUT_NUMBER',
    100000,
  ),
  API_CHARACTERS_REACHED_ZERO_NUMBER: getEnvVarInt(
    'API_CHARACTERS_REACHED_ZERO_NUMBER',
    1000,
  ),

  TTS_GATE_URL: process.env.TTS_GATE_URL,
  AI_VOICE_APP_ID: process.env.AI_VOICE_APP_ID,
  AI_VOICE_TOKEN: process.env.AI_VOICE_TOKEN,
  AI_VOICE_CALLBACK_URL: process.env.AI_VOICE_CALLBACK_URL,
  VBEE_DUBBING_URL: process.env.VBEE_DUBBING_URL,
  PAYMENT_URL: process.env.PAYMENT_URL,

  RECAPTCHA_SECRET_KEY: process.env.RECAPTCHA_SECRET_KEY,
  RECAPTCHA_ENTERPRISE_PROJECT_ID: process.env.RECAPTCHA_ENTERPRISE_PROJECT_ID,
  RECAPTCHA_ENTERPRISE_CREDENTIAL_CLIENT_EMAIL:
    process.env.RECAPTCHA_ENTERPRISE_CREDENTIAL_CLIENT_EMAIL,
  RECAPTCHA_ENTERPRISE_CREDENTIAL_PRIVATE_KEY:
    process.env.RECAPTCHA_ENTERPRISE_CREDENTIAL_PRIVATE_KEY,
  RECAPTCHA_ENTERPRISE_WEBSITE_SITE_KEY:
    process.env.RECAPTCHA_ENTERPRISE_WEBSITE_SITE_KEY,
  RECAPTCHA_ENTERPRISE_ANDROID_SITE_KEY:
    process.env.RECAPTCHA_ENTERPRISE_ANDROID_SITE_KEY,
  RECAPTCHA_ENTERPRISE_IOS_SITE_KEY:
    process.env.RECAPTCHA_ENTERPRISE_IOS_SITE_KEY,
  RECAPTCHA_ERROR_SLACK_CHANNEL: process.env.RECAPTCHA_ERROR_SLACK_CHANNEL,

  GROWTH_BOOK_API_HOST: process.env.GROWTH_BOOK_API_HOST,
  GROWTH_BOOK_CLIENT_KEY: process.env.GROWTH_BOOK_CLIENT_KEY,
  LOADING_FEATURES_REALTIME_INTERVAL: getEnvVarInt(
    'LOADING_FEATURES_REALTIME_INTERVAL',
    1,
  ), // minutes
  MAX_TITLE_LENGTH: 50,

  DOWNSTREAM_HEALTHCHECK_INTERVAL: getEnvVarInt(
    'DOWNSTREAM_HEALTHCHECK_INTERVAL',
    30000,
  ), // milliseconds

  SYNC_ACCOUNT_FROM_IAM_INTERVAL: getEnvVarInt(
    'SYNC_ACCOUNT_FROM_IAM_INTERVAL',
    300000,
  ), // milliseconds

  NEW_RELIC_ENABLED: process.env.NEW_RELIC_ENABLED === 'true',

  MOE_APP_ID: process.env.MOE_APP_ID,
  MOE_API_KEY: process.env.MOE_API_KEY,
  MOE_DASHBOARD: process.env.MOE_DASHBOARD,

  DATASENSES_URL: process.env.DATASENSES_URL,
  DATASENSES_KEY: process.env.DATASENSES_KEY,

  STT_GATE_URL: process.env.STT_GATE_URL,
  AIV_STT_CALLBACK_URL: process.env.AIV_STT_CALLBACK_URL,
  STT_APP_ID: process.env.STT_APP_ID,
  STT_TOKEN: process.env.STT_TOKEN,
  STT_S3_BUCKET: process.env.STT_S3_BUCKET,
  STT_S3_REGION: process.env.STT_S3_REGION,
  STT_S3_ACCESS_KEY_ID: process.env.STT_S3_ACCESS_KEY_ID,
  STT_S3_SECRET_ACCESS_KEY: process.env.STT_S3_SECRET_ACCESS_KEY,
  MICROSOFT_REGION: process.env.MICROSOFT_REGION,
  MICROSOFT_SPEECH_KEY: process.env.MICROSOFT_SPEECH_KEY,

  SENTRY_DSN: process.env.SENTRY_DSN,
  // this will return true if and only if the envvar is explicitly set to 'true'
  SENTRY_ENABLED: EnvironmentHelper.getEnvVarBoolean('SENTRY_ENABLED', false),
  SENTRY_TRACES_SAMPLE_RATE: EnvironmentHelper.getEnvVarFloat(
    'SENTRY_TRACES_SAMPLE_RATE',
    1.0,
  ),
  SENTRY_PROFILES_SAMPLE_RATE: getEnvVarFloat(
    'SENTRY_PROFILES_SAMPLE_RATE',
    1.0,
  ),
  CLOUD_RUN_CLIENT_EMAIL: process.env.CLOUD_RUN_CLIENT_EMAIL,
  CLOUD_RUN_PRIVATE_KEY: process.env.CLOUD_RUN_PRIVATE_KEY,
  CLOUD_RUN_JOIN_URL: process.env.CLOUD_RUN_JOIN_URL,

  VOICE_CLONING_APP_ID: process.env.VOICE_CLONING_APP_ID,
  VOICE_CLONING_TOKEN: process.env.VOICE_CLONING_TOKEN,
};
