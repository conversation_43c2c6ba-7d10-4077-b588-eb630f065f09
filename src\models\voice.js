const mongoose = require('mongoose');
const { VOICE_PROVIDER, VOICE_LEVEL } = require('../constants');

const voiceSchema = new mongoose.Schema(
  {
    code: String,
    ttsGateCode: String,
    name: String,
    image: String,
    gender: { type: String, enum: ['male', 'female'] },
    languageCode: String,
    secondaryLanguageCodes: [String],
    type: String,
    provider: {
      type: String,
      enum: Object.values(VOICE_PROVIDER),
    },
    squareImage: String,
    roundImage: String,
    demo: String,
    rank: { type: Number, default: 999 },
    features: [String],
    styles: [String],
    sampleRates: [Number],
    defaultSampleRate: Number,
    cachingFunction: String,
    synthesisFunction: String,
    active: Boolean,
    global: Boolean,
    level: {
      type: String,
      enum: Object.values(VOICE_LEVEL),
    },
    version: String,
    beta: Boolean,
    isSample: { type: Boolean, default: false },
    sample: {
      style: String,
      audioLink: String,
      text: String,
    },
    hasDubbing: { type: Boolean, default: false },
    eolDate: Date,
    canonicalVoiceCode: String,
    locale: String,
    category: String,
    ageGroup: String,
    description: String,
  },
  {
    versionKey: false,
    timestamps: true,
    toObject: { virtuals: true },
    toJSON: { virtuals: true },
  },
);

voiceSchema.virtual('canonicalVoice', {
  ref: 'Voice',
  localField: 'canonicalVoiceCode',
  foreignField: 'code',
  justOne: true,
});

voiceSchema.pre(/^find/, function (next) {
  this.populate({
    path: 'canonicalVoice',
    select: 'code name',
  });
  next();
});

module.exports = mongoose.model('Voice', voiceSchema);
