const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const { authAPI } = require('../middlewares/auth');
const { checkBlockedUserApi } = require('../middlewares/checkBlockedUser');
const { blockBlackWords } = require('../middlewares/blockBlackWords');

const synthesisApiController = require('../controllers/api');
const requestController = require('../controllers/request');
const callbackResultController = require('../controllers/callbackResult');
const ttsController = require('../controllers/tts');

const { synthesisApiValidate } = require('../validations/api');

/* eslint-disable prettier/prettier */
router.post(
  '/tts',
  authAPI,
  checkBlockedUserApi,
  synthesisApiValidate,
  blockBlackWords,
  asyncMiddleware(synthesisApiController.apiSynthesis),
);
router.get('/tts/:requestId', asyncMiddleware(requestController.getApiRequest));
router.get(
  '/tts/:requestId/callback-result',
  asyncMiddleware(callbackResultController.getCallbackResult),
);

// callback from tts gate
router.put(
  '/tts/update-request-progress',
  asyncMiddleware(synthesisApiController.updateRequestProgress),
);
router.post(
  '/tts/callback-tts-gate',
  asyncMiddleware(synthesisApiController.apiCallbackResponse),
);

router.post('/tts/callback', asyncMiddleware(ttsController.handleCallbackTTS));

/* eslint-disable prettier/prettier */
module.exports = router;
