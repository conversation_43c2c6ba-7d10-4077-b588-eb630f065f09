const moment = require('moment');
const {
  getAggregateUserVoiceStats,
  getAggregateCommunityVoiceStats,
} = require('../daos/communityVoiceStats');
const {
  findVoiceCloningByCodes,
  findVoiceCloningsByNameForUser,
  findVoiceCloningByIdentifier,
  bulkUpdateVoiceStats,
} = require('../daos/voiceCloning');
const { getAccessToken } = require('./iam');
const callApi = require('../utils/callApi');
const { VOICE_CLONING_URL } = require('../configs');
const code = require('../errors/code');
const CustomError = require('../errors/CustomError');
const {
  COMMUNITY_VOICE_STATS_EVENT,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
} = require('../constants');
const Caching = require('../caching');

const getVoiceFromServiceVoiceCloning = async (voiceCode) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VOICE_CLONING_URL}/api/v1/voice-cloning/admin/voices`,
      method: 'GET',
      params: {
        code: voiceCode,
      },
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new CustomError(code.INTERNAL_SERVER_ERROR);

    return result?.voices?.[0];
  } catch (err) {
    logger.error(err, { ctx: 'GetVoiceFromServiceVoiceCloning', voiceCode });
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Failed to get voice from service voice cloning',
    );
  }
};

const defaultVoiceCommunityStats = {
  totalCharacters: 0,
  totalCredits: 0,
  totalMoneyUsd: 0,
  totalMoneyVnd: 0,
  totalRequests: 0,
  totalUsers: 0,
};

const getUserVoiceStats = async ({ userId, startDate, endDate, search }) => {
  const matchQuery = { voiceOwnerId: userId };
  let voiceCodes = [];
  let voiceList = [];

  if (search) {
    voiceList = await findVoiceCloningsByNameForUser(search, userId);
    voiceCodes = voiceList.map((v) => v.code);
  }

  if (startDate) {
    matchQuery.date = {
      ...matchQuery.date,
      $gte: moment(startDate).startOf('day').toDate(),
    };
  }
  if (endDate) {
    matchQuery.date = {
      ...matchQuery.date,
      $lte: moment(endDate).endOf('day').toDate(),
    };
  }
  if (search) {
    matchQuery.voiceCode = { $in: voiceCodes };
  }

  const userVoiceStats = await getAggregateUserVoiceStats(matchQuery);

  if (!search) {
    voiceCodes = userVoiceStats.communityVoices.map((v) => v.voiceCode);
    voiceList = await findVoiceCloningByCodes(voiceCodes);
  }

  const enrichedCommunityVoices = userVoiceStats.communityVoices.map((v) => {
    const voiceInfo = voiceList.find((voice) => voice.code === v.voiceCode);
    return { ...v, voice: voiceInfo };
  });

  return {
    summary: {
      ...userVoiceStats.summary,
      totalCommunityVoice: enrichedCommunityVoices.length,
    },
    communityVoices: enrichedCommunityVoices,
  };
};

const getCommunityVoiceStats = async ({ voiceId, voiceCode, key }) => {
  const voiceInfo = await findVoiceCloningByIdentifier({
    voiceId,
    voiceCode,
    key,
  });

  if (!voiceInfo)
    return {
      ...defaultVoiceCommunityStats,
      voice: null,
    };

  const matchQuery = {};
  if (voiceInfo) matchQuery.voiceCode = voiceInfo.code;

  // TODO: Handle cache

  const voiceFromServiceVoiceCloning = await getVoiceFromServiceVoiceCloning(
    voiceInfo.code,
  );

  const { communityVoiceStats } =
    (await getAggregateCommunityVoiceStats(matchQuery)) || [];

  const finalVoiceStats = communityVoiceStats[0] || defaultVoiceCommunityStats;

  return {
    ...finalVoiceStats,
    voice: {
      ...voiceInfo,
      ageGroup: voiceFromServiceVoiceCloning?.ageGroup,
      description: voiceFromServiceVoiceCloning?.description,
    },
  };
};

const getBulkCommunityVoiceStats = async ({ voiceCodes, fromDate, toDate }) => {
  if (!voiceCodes || voiceCodes.length === 0) {
    return {
      results: [],
    };
  }

  try {
    const matchQuery = { voiceCodes };
    if (fromDate) matchQuery.fromDate = fromDate;
    if (toDate) matchQuery.toDate = toDate;

    const bulkStats = await getAggregateCommunityVoiceStats(matchQuery);

    return {
      results: bulkStats?.communityVoiceStats || [],
    };
  } catch (error) {
    logger.error(error, { ctx: 'getBulkCommunityVoiceStats' });
    return {
      results: [],
    };
  }
};

const prepareUpdatesData = ({
  allTimeResults,
  last30DaysResults,
  voiceCodes,
}) => {
  const statsUpdates = {};
  const stats30dUpdates = {};

  const extractStatsData = (voiceStats) => ({
    totalCharacters: voiceStats.totalCharacters,
    totalCredits: voiceStats.totalCredits,
    totalMoneyUsd: voiceStats.totalMoneyUsd,
    totalMoneyVnd: voiceStats.totalMoneyVnd,
    totalRequests: voiceStats.totalRequests,
    totalUsers: voiceStats.totalUsers,
  });

  allTimeResults.forEach((voiceStats) => {
    if (voiceStats.voiceCode) {
      statsUpdates[voiceStats.voiceCode] = extractStatsData(voiceStats);
    }
  });

  last30DaysResults.forEach((voiceStats) => {
    if (voiceStats.voiceCode) {
      stats30dUpdates[voiceStats.voiceCode] = extractStatsData(voiceStats);
    }
  });

  voiceCodes.forEach((voiceCode) => {
    if (!statsUpdates[voiceCode]) {
      statsUpdates[voiceCode] = { ...defaultVoiceCommunityStats };
    }
    if (!stats30dUpdates[voiceCode]) {
      stats30dUpdates[voiceCode] = { ...defaultVoiceCommunityStats };
    }
  });

  return { statsUpdates, stats30dUpdates };
};

const handleCommunityVoiceStats = async (data) => {
  const { date, voiceCodes, event } = data;

  if (
    [
      COMMUNITY_VOICE_STATS_EVENT.SYNC_VOICE_STATS_TO_VOICE,
      COMMUNITY_VOICE_STATS_EVENT.DAILY_VOICE_STATS_COMPLETED,
    ].includes(event)
  ) {
    const dateKey = moment(date).format('YYYY-MM-DD');
    const key = `${REDIS_KEY_PREFIX.COMMUNITY_VOICE_STATS}:${event}:${dateKey}`;
    const isStatsAlreadyProcessed = await Caching.RedisRepo.get(key);

    if (isStatsAlreadyProcessed) {
      logger.info(`Voice stats already updated for ${dateKey}`, {
        ctx: 'handleCommunityVoiceStats',
      });
      return;
    }

    const chunkSize = 100;
    for (let i = 0; i < voiceCodes.length; i += chunkSize) {
      const chunk = voiceCodes.slice(i, i + chunkSize);

      const [allTimeStats, last30DaysStats] = await Promise.all([
        getBulkCommunityVoiceStats({
          voiceCodes: chunk,
        }),
        getBulkCommunityVoiceStats({
          voiceCodes: chunk,
          fromDate: moment().subtract(30, 'days').toDate(),
          toDate: moment().toDate(),
        }),
      ]);

      const { statsUpdates, stats30dUpdates } = prepareUpdatesData({
        allTimeResults: allTimeStats.results,
        last30DaysResults: last30DaysStats.results,
        voiceCodes: chunk,
      });

      await Promise.all([
        bulkUpdateVoiceStats(voiceCodes, statsUpdates, 'stats'),
        bulkUpdateVoiceStats(voiceCodes, stats30dUpdates, 'stats30d'),
      ]);
    }

    await Caching.RedisRepo.set(key, 1, REDIS_KEY_TTL.COMMUNITY_VOICE_STATS);
    logger.info(`Voice stats updated for ${dateKey}`, {
      ctx: 'handleCommunityVoiceStats',
    });
  }

  // TODO: Handle other events
};

module.exports = {
  getUserVoiceStats,
  getCommunityVoiceStats,
  handleCommunityVoiceStats,
};
