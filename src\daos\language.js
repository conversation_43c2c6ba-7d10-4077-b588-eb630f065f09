const Language = require('../models/language');
const daoUtils = require('./utils');

const createLanguages = async (languages) => {
  await Language.insertMany(languages);
};

const findLanguages = async (query = {}) => {
  const { documents: languages, total } = await daoUtils.find(Language, query);
  return {
    languages: languages.map((language) => {
      delete language._id;
      return language;
    }),
    total,
  };
};

const findLanguageByCode = async (code) => {
  const language = await Language.findOne({ code }).lean();
  return language;
};

const groupByLanguageWithRegion = async () => {
  const languages = await Language.aggregate([
    {
      $addFields: {
        primaryLanguageSubTag: {
          $arrayElemAt: [{ $split: ['$code', '-'] }, 0],
        },
        regionSubTag: { $arrayElemAt: [{ $split: ['$code', '-'] }, 1] },
      },
    },
    {
      $group: {
        _id: '$primaryLanguageSubTag',
        regions: {
          $push: {
            regionSubTag: '$regionSubTag',
            name: {
              en: { $ifNull: ['$region.en', ''] },
              vi: { $ifNull: ['$region.vi', ''] },
            },
          },
        },
        name: { $first: '$name' },
      },
    },
    {
      $project: {
        _id: 0,
        primaryLanguageSubTag: '$_id',
        regions: 1,
        name: 1,
      },
    },
  ]);

  return languages;
};

module.exports = {
  createLanguages,
  findLanguages,
  findLanguageByCode,
  groupByLanguageWithRegion,
};
