const retry = require('retry');
const errorCode = require('../errors/code');
const CustomError = require('../errors/CustomError');
const { TTS_GATE_URL, AI_VOICE_TOKEN, AI_VOICE_APP_ID } = require('../configs');

const logger = require('../utils/logger');
const callApi = require('../utils/callApi');

class TtsGateAdapter {
  static async execute({ apiEndpoint, data, params, method = 'POST' }) {
    const res = await callApi({
      headers: { authorization: `Bearer ${AI_VOICE_TOKEN}` },
      url: `${TTS_GATE_URL}/api/v1/${apiEndpoint}`,
      method,
      data,
      params,
    });

    return res;
  }

  static async executeWithRetry({
    apiEndpoint,
    data,
    params,
    method = 'POST',
    requestId,
  }) {
    return new Promise((resolve, reject) => {
      const operation = retry.operation({
        retries: 3, // Maximum 3 retries
        factor: 2, // Exponential backoff factor
        minTimeout: 2000, // Start with 2 second delay
        maxTimeout: 20000, // Max 20 seconds between retries
        randomize: true, // Add some randomization to prevent thundering herd
      });

      operation.attempt(async (currentAttempt) => {
        try {
          const result = await TtsGateAdapter.execute({
            apiEndpoint,
            data,
            params,
            method,
          });
          resolve(result);
        } catch (err) {
          if (operation.retry(err)) {
            logger.warn(
              `TTS Gate synthesis attempt ${currentAttempt} failed, retrying...`,
              {
                ctx: 'TtsGateAdapter.executeWithRetry',
                method,
                apiEndpoint,
                requestId,
              },
            );
            return;
          }
          reject(operation.mainError());
        }
      });
    });
  }

  static async executeGet({ apiEndpoint, params }) {
    return TtsGateAdapter.execute({
      apiEndpoint,
      method: 'GET',
      params,
      data: undefined,
    });
  }

  static async getUptime() {
    return TtsGateAdapter.executeGet({
      apiEndpoint: 'uptime',
      params: undefined,
    });
  }

  static async getTts(params) {
    return TtsGateAdapter.executeGet({
      apiEndpoint: 'tts',
      params,
    });
  }

  static async createVoice(ttsPayload = {}) {
    const res = await TtsGateAdapter.execute({
      apiEndpoint: 'voices',
      method: 'POST',
      params: undefined,
      data: {
        ...ttsPayload,
        appId: AI_VOICE_APP_ID,
        code: ttsPayload?.ttsGateCode || ttsPayload?.code,
      },
    });

    if (res.status !== 1) {
      throw new CustomError(
        errorCode.CALL_API_CREATE_VOICE_IN_TTS_GATE_FAILED,
        res.message,
      );
    }

    return res;
  }
}

module.exports = {
  TtsGateAdapter,
};
