const moment = require('moment');
const { Constants } = require('@vbee-holding/vbee-tts-models');
const CustomError = require('../errors/CustomError');
const errorCode = require('../errors/code');
const logger = require('../utils/logger');

const voiceCloningDao = require('../daos/voiceCloning');

const {
  LANGUAGE_CODE,
  VC_SAMPLE_RATES,
  VC_DEFAULT_SAMPLE_RATE,
  VOICE_STATUS,
} = require('../constants/voiceCloning');
const {
  TTS_API_URL,
  VOICE_CLONING_APP_ID,
  VOICE_CLONING_TOKEN,
  S3_BUCKET,
  GCS_BUCKET,
} = require('../configs');
const {
  VOICE_PROVIDER,
  VOICE_CLONING_TYPE,
  VOICE_TYPE,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  REQUEST_STATUS,
} = require('../constants');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const ttsService = require('./tts');
const {
  getVoiceCloningSampleScript,
} = require('../daos/voiceCloningSampleScript');

const { TtsGateAdapter } = require('../adapters/ttsgate');

const Caching = require('../caching');
const { copyFileCloudToCloud } = require('./audio');
const { getRandomItems } = require('../utils/array');
const { TTS_CALLBACK_ACTION } = require('../constants/tts');

/** VoiceCloning code: start with prefix [ncs], someName(province), gender, username, VC category (podcast, or callcenter), suffix (vc) */
const VC_VOICE_CODE_REGEX =
  /[ncs]_[a-z]+_(male|female)_[a-z0-9]+(_zero_shot)?_[a-z]+_vc+$/;

/** check code string to see is it match VC standard of naming */
const isVoiceCloningCode = (code) => VC_VOICE_CODE_REGEX.test(code);

const getTTSGateVoiceType = (code, type) => {
  const isClonedVoice = isVoiceCloningCode(code);
  const isZeroShotVoice =
    isClonedVoice && type === VOICE_CLONING_TYPE.ZERO_SHOT;

  if (isZeroShotVoice) return VOICE_TYPE.ZERO_SHOT;

  if (isClonedVoice) return VOICE_TYPE.CLONING;

  return VOICE_TYPE.TTS;
};

const createVoiceCloningVoice = async ({
  userId,
  code,
  name,
  gender,
  avatar,
  locale,
  province,
  status,
  type,
  ttsGateCode,
  sampleLink,
}) => {
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);

  const createVoiceData = {
    code,
    name,
    image: avatar,
    gender,
    locale,
    province,
    status,
    languageCode: LANGUAGE_CODE.VI,
    provider: VOICE_PROVIDER.VBEE_VOICE_CLONING,
    squareImage: avatar,
    roundImage: avatar,
    sampleRates: VC_SAMPLE_RATES,
    defaultSampleRate: VC_DEFAULT_SAMPLE_RATE,
    type,
    ttsGateCode,
  };

  if (!voice) await TtsGateAdapter.createVoice(createVoiceData);

  const ttsGateVoiceType = getTTSGateVoiceType(code, type);

  const voiceCloningVoice = await voiceCloningDao.createVoiceCloningVoice({
    ...createVoiceData,
    userId,
    type: ttsGateVoiceType,
    demo: sampleLink,
  });

  return voiceCloningVoice;
};

/**
  1. `isValidateVoice` is used to prevent crash layout of project screen
      More info in func getVoiceInfoByCodes, getProject
      TODO: Dont base on input to split action of a function (high coupling)
      TODO: Add requestType to prevent api request can use public voice
  2. This func also get voice for get sample audio in pro voice cloning (from route /api/v1/tts)
   But when voice-cloning call to vbee-tts-api, voice doesnt have status, so at the moment, we still need to check status
   TODO: Mark a request come from voice-cloning or not, if true => dont need to check voice condition
*/
const getClonedVoiceByCode = async ({
  code,
  userId,
  isValidateVoice = true,
  canUseProVoiceCloning,
  canUseInstantVoiceCloning,
}) => {
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);
  if (!voice) throw new CustomError(errorCode.INVALID_VOICE_CODE);

  // TODO: Remove FF
  const canUsePublicVoice = getFeatureValue(
    FEATURE_KEYS.USE_PUBLIC_VOICE_CLONING,
    { userId, voiceCode: code },
  );

  const {
    status,
    userId: voiceOwnerId,
    discardAt,
    type = VOICE_CLONING_TYPE.FINETUNE_SCRIPTS_RECORDING,
  } = voice;

  if (!isValidateVoice) return voice;

  if (status === VOICE_STATUS.PUBLIC) {
    if (!canUsePublicVoice) throw new CustomError(errorCode.INVALID_VOICE_CODE);

    if (discardAt && moment().isAfter(discardAt))
      throw new CustomError(errorCode.DISCARDED_VOICE_CLONING);

    return voice;
  }

  if (status === VOICE_STATUS.PRIVATE) {
    // Voice is private but users use voice of another user
    if (userId !== voiceOwnerId)
      throw new CustomError(errorCode.DISCARDED_VOICE_CLONING);

    // Voice is private, users use their own voice but now they are not permitted to use this voice
    // Becase we compare userId and voiceOwnerId before, so from this to end of func, we dont need to compare them again
    if (
      type === VOICE_CLONING_TYPE.FINETUNE_SCRIPTS_RECORDING &&
      !canUseProVoiceCloning
    )
      throw new CustomError(errorCode.DISCARDED_VOICE_CLONING);

    // At this time, only studio request can use instant voice so we need this condition
    if (type === VOICE_CLONING_TYPE.ZERO_SHOT && !canUseInstantVoiceCloning)
      throw new CustomError(errorCode.DISCARDED_VOICE_CLONING);
  }

  return voice;
};

const requestSampleAudioTTS = async ({ voiceCode, sampleAudio, action }) => {
  const res = await ttsService.callApiSynthesis({
    appId: VOICE_CLONING_APP_ID,
    appToken: VOICE_CLONING_TOKEN,
    voiceCode,
    inputText: sampleAudio.text,
    speedRate: 1,
    audioType: Constants.AUDIO_TYPE.MP3,
    callbackUrl: `${TTS_API_URL}/api/v1/tts/callback?action=${action}`,
  });

  const sampleAudioId = sampleAudio._id?.toString();
  const { requestId } = res?.result || {};

  if (!requestId || !sampleAudioId) {
    throw new CustomError(errorCode.INVALID_VOICE_CODE_FOR_VC_SAMPLE_AUDIO);
  }

  const sampleAudioData = {
    voiceCode,
    sampleAudioId,
  };

  const key = `${REDIS_KEY_PREFIX.VC_SAMPLE_AUDIO}:${requestId}`;
  const ttl = REDIS_KEY_TTL.VC_SAMPLE_AUDIO;
  await Caching.RedisRepo.set(key, JSON.stringify(sampleAudioData), ttl);

  return res;
};

const generateSampleAudios = async (voice, action) => {
  await Promise.all(
    voice?.sampleAudios.map((sampleAudio) =>
      requestSampleAudioTTS({
        voiceCode: voice.code,
        sampleAudio,
        action,
      }),
    ),
  );
};

const updateClonedVoice = async (updateData) => {
  const {
    userId,
    code,
    name,
    avatar,
    status,
    demo,
    active,
    retentionDays,
    discardAt,
    category,
    publishAt,
    ageGroup,
    description,
  } = updateData;
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);
  if (!voice) throw new CustomError(errorCode.INVALID_VOICE_CODE);
  if (voice.userId !== userId)
    throw new CustomError(errorCode.INVALID_VOICE_CODE);

  const updateVoiceData = {
    name,
    image: avatar,
    squareImage: avatar,
    roundImage: avatar,
  };

  if (status) updateVoiceData.status = status;
  if (demo) updateVoiceData.demo = demo;
  if (active !== undefined && active !== null) updateVoiceData.active = active;
  if (retentionDays !== undefined && retentionDays !== null)
    updateVoiceData.retentionDays = retentionDays;
  if (discardAt) updateVoiceData.discardAt = discardAt;
  if (category) updateVoiceData.category = category;
  if (publishAt) updateVoiceData.publishAt = publishAt;
  if (ageGroup) updateVoiceData.ageGroup = ageGroup;
  if (description) updateVoiceData.description = description;
  const isFirstTimePublicVoice =
    status === VOICE_STATUS.PUBLIC &&
    (!voice?.sampleAudios || voice?.sampleAudios?.length === 0);

  if (isFirstTimePublicVoice) {
    const sampleScript = await getVoiceCloningSampleScript(
      voice?.category || category,
    );
    const { sampleScripts } = sampleScript || [];
    const selectedSampleAudios = getRandomItems(sampleScripts, 10);
    updateVoiceData.sampleAudios = selectedSampleAudios;
  }

  const updatedVoice = await voiceCloningDao.updateClonedVoiceInfo(
    code,
    updateVoiceData,
  );

  if (isFirstTimePublicVoice)
    generateSampleAudios(
      updatedVoice,
      TTS_CALLBACK_ACTION.UPDATE_VC_SAMPLE_AUDIOS,
    );
};

const getVoiceCloningVoice = async (code) => {
  // ADVISE: PERFORMANCE: should be cache using CacheService
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);
  return voice;
};

const getCommunityVoiceCloningForUser = async (userId) => {
  const voices = await voiceCloningDao.findCommunityVoiceCloningForUser(userId);
  return voices;
};

const updateSampleAudioLink = async ({
  requestId,
  audioLink,
  audioType = 'mp3',
  status,
}) => {
  if (status !== REQUEST_STATUS.SUCCESS) return;

  const key = `${REDIS_KEY_PREFIX.VC_SAMPLE_AUDIO}:${requestId}`;
  const sampleAudioData = await Caching.RedisRepo.get(key);
  if (!sampleAudioData) {
    logger.error(`${key} not found in Redis`, {
      ctx: 'updateSampleAudioLink',
      requestId,
      audioLink,
    });
    throw new CustomError(errorCode.KEY_NOT_FOUND_IN_REDIS);
  }

  let sampleAudioDataObj;
  try {
    sampleAudioDataObj = JSON.parse(sampleAudioData);
  } catch (err) {
    logger.error(`${key} is not a valid JSON`, {
      ctx: 'updateSampleAudioLink',
      requestId,
      audioLink,
      err,
    });
    throw new CustomError(errorCode.INVALID_JSON_IN_REDIS);
  }

  const { voiceCode, sampleAudioId } = sampleAudioDataObj;
  const voice = await voiceCloningDao.findVoiceCloningByCode(voiceCode);
  if (!voice) throw new CustomError(errorCode.INVALID_VOICE_CODE);

  const voiceId = voice._id.toString();
  const finalAudioLink = await copyFileCloudToCloud({
    sourceLink: audioLink,
    destinationKeyS3: `samples/voice-${voiceId}/${sampleAudioId}.${audioType}`,
    destinationKeyGCS: `voice-cloning/samples/voice-${voiceId}/${sampleAudioId}.${audioType}`,
    destinationBucketS3: S3_BUCKET.VOICE_CLONING,
    destinationBucketGCS: GCS_BUCKET.AI_VOICE,
  });

  await voiceCloningDao.updateSampleAudioLinkById({
    voiceCode,
    sampleAudioId,
    audioLink: finalAudioLink,
  });
};

const processNextSampleAudio = async ({ requestId, status, audioLink }) => {
  await updateSampleAudioLink({
    requestId,
    audioLink,
    status,
  });

  const nextSampleAudio = await Caching.GlobalQueue.dequeue(
    REDIS_KEY_PREFIX.VC_SAMPLE_AUDIOS_QUEUE,
  );
  if (!nextSampleAudio) return;

  const nextSampleAudioData = JSON.parse(nextSampleAudio);
  await requestSampleAudioTTS({
    voiceCode: nextSampleAudioData.voiceCode,
    sampleAudio: nextSampleAudioData.sampleAudio,
    action: TTS_CALLBACK_ACTION.PROCESS_NEXT_SAMPLE_AUDIO,
  });
};

const updateVoiceCloningProfilePage = async ({
  userId,
  voiceCode,
  status,
  sampleAudioIds,
  coverImage,
  slug,
  subCoverImage,
}) => {
  const currentVoice = await voiceCloningDao.findVoiceCloningByIdentifier({
    voiceCode,
  });
  if (!currentVoice) throw new CustomError(errorCode.INVALID_VOICE_CODE);
  if (currentVoice.userId !== userId)
    throw new CustomError(errorCode.FORBIDDEN);

  if (/_vc$/i.test(slug) && slug !== voiceCode)
    throw new CustomError(
      errorCode.INVALID_SLUG,
      'Slug must not end with "_vc"',
    );

  const voiceWithSameSlug = await voiceCloningDao.findVoiceCloningByIdentifier({
    slug,
  });
  if (voiceWithSameSlug && voiceWithSameSlug.code !== voiceCode)
    throw new CustomError(errorCode.SLUG_ALREADY_EXIST);

  const finalSlug = slug === voiceCode ? undefined : slug;

  const existingSampleIds = currentVoice.sampleAudios.map((sa) =>
    sa._id.toString(),
  );
  const invalidIds = sampleAudioIds.filter(
    (id) => !existingSampleIds.includes(id),
  );
  if (invalidIds.length > 0) {
    throw new CustomError(
      errorCode.INVALID_PROFILE_PAGE_SAMPLE_AUDIOS,
      `Invalid sampleAudios: ${invalidIds.join(', ')}`,
    );
  }

  await voiceCloningDao.updateVoiceCloningProfilePage(voiceCode, {
    status,
    sampleAudioIds,
    coverImage,
    slug: finalSlug,
    subCoverImage,
  });
};

module.exports = {
  createVoiceCloningVoice,
  getClonedVoiceByCode,
  updateClonedVoice,
  isVoiceCloningCode,
  getVoiceCloningVoice,
  getCommunityVoiceCloningForUser,
  updateSampleAudioLink,
  updateVoiceCloningProfilePage,
  generateSampleAudios,
  processNextSampleAudio,
  requestSampleAudioTTS,
};
