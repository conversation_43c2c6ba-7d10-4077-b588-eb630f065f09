/* eslint-disable no-plusplus */
const getSameElementsIn2Array = (arr1, arr2) => {
  const set = new Set(arr2);
  return arr1.filter((element) => set.has(element));
};

const hasSameElementIn2Array = (arr1 = [], arr2 = []) => {
  const set = new Set(arr2);
  return arr1.some((element) => set.has(element));
};

const getRandomItems = (array, count) => {
  if (!Array.isArray(array) || array.length === 0) return [];
  if (count >= array.length) return [...array];

  const arr = [...array];
  for (let i = arr.length - 1; i > arr.length - count - 1; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr.slice(arr.length - count);
};

module.exports = {
  getSameElementsIn2Array,
  hasSameElementIn2Array,
  getRandomItems,
};
