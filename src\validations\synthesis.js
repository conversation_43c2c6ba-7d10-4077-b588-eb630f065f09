const { Joi, validate } = require('express-validation');
const { ObjectId } = require('mongoose').Types;
const { Constants } = require('@vbee-holding/vbee-tts-models');
const {
  VALID_SPEED,
  VALID_BIT_RATE,
  VALID_BACKGROUND_MUSIC_VOLUME,
  OUTPUT_TYPE,
  VOICE_PROVIDER,
} = require('../constants');
const { VALID_CLIENT_PAUSE } = require('../constants/clientPause');

const sentence = Joi.object().keys({
  text: Joi.string().required(),
  voiceCode: Joi.string().required(),
  voiceProvider: Joi.string()
    .valid(...Object.values(VOICE_PROVIDER))
    .optional(),
  speed: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
  breakTime: Joi.number().optional(),
});

const synthesisValidate = {
  body: Joi.object({
    outputType: Joi.string()
      .valid(...Object.values(OUTPUT_TYPE))
      .optional(),
    text: Joi.string().trim().when('sentences', {
      not: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
    voiceCode: Joi.string().when('text', {
      is: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
    sentences: Joi.array().min(1).items(sentence).optional(),
    title: Joi.string().optional(),
    audioType: Joi.string()
      .valid(...Object.values(Constants.AUDIO_TYPE))
      .optional(),
    bitrate: Joi.number()
      .valid(...VALID_BIT_RATE)
      .optional()
      .messages({
        'any.only': 'Invalid bitrate value',
      }),
    sampleRate: Joi.number().optional(),
    speed: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
    backgroundMusic: Joi.object({
      link: Joi.string().trim().optional(),
      name: Joi.string().optional(),
      volume: Joi.number()
        .min(VALID_BACKGROUND_MUSIC_VOLUME.MIN)
        .max(VALID_BACKGROUND_MUSIC_VOLUME.MAX)
        .default(VALID_BACKGROUND_MUSIC_VOLUME.DEFAULT)
        .optional(),
    }).optional(),
    clientPause: Joi.object({
      paragraphBreak: Joi.number()
        .min(0)
        .max(VALID_CLIENT_PAUSE.MAX)
        .optional(),
      sentenceBreak: Joi.number()
        .min(VALID_CLIENT_PAUSE.MIN)
        .max(VALID_CLIENT_PAUSE.MAX)
        .optional(),
      majorBreak: Joi.number()
        .min(VALID_CLIENT_PAUSE.MIN)
        .max(VALID_CLIENT_PAUSE.MAX)
        .optional(),
      mediumBreak: Joi.number()
        .min(VALID_CLIENT_PAUSE.MIN)
        .max(VALID_CLIENT_PAUSE.MAX)
        .optional(),
    }).optional(),
    datasenses: Joi.object().optional(),
    version: Joi.string().optional(),
    paragraphs: Joi.array().items(Joi.object()).optional(),
    blockId: Joi.string().optional(),
    projectId: Joi.string()
      .optional()
      .custom((value, helper) => {
        if (ObjectId.isValid(value)) return true;
        return helper.message('projectId is invalid');
      }),
  })
    .without('text', 'sentences')
    .custom((value, helpers) => {
      const { blockId, projectId } = value;
      if (projectId) {
        if (!blockId) {
          return helpers.message('Must provide both "blockId" and "projectId"');
        }
      }
      return value;
    }),
};

module.exports = {
  synthesisValidate: validate(synthesisValidate, { keyByField: true }),
};
