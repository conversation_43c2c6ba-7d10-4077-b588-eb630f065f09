const communityVoiceStatsService = require('../services/communityVoiceStats');

const getUserVoiceStats = async (req, res) => {
  const { userId } = req.user;
  const { startDate, endDate, search } = req.query;

  const userVoiceStats = await communityVoiceStatsService.getUserVoiceStats({
    userId,
    startDate,
    endDate,
    search,
  });
  return res.send(userVoiceStats);
};

const getUserVoiceStatsByUserId = async (req, res) => {
  const { userId } = req.params;
  const { startDate, endDate, search } = req.query;

  const userVoiceStats = await communityVoiceStatsService.getUserVoiceStats({
    userId,
    startDate,
    endDate,
    search,
  });
  return res.send(userVoiceStats);
};

const getCommunityVoiceStats = async (req, res) => {
  const { voiceId, voiceCode, key } = req.query;

  const communityVoiceStats =
    await communityVoiceStatsService.getCommunityVoiceStats({
      voiceId,
      voiceCode,
      key,
    });
  return res.send(communityVoiceStats);
};

module.exports = {
  getUserVoiceStats,
  getUserVoiceStatsByUserId,
  getCommunityVoiceStats,
};
