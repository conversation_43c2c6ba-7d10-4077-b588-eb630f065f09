const moment = require('moment');
const cheerio = require('cheerio');
const logger = require('../utils/logger');
const {
  EMPHASIS_LEVEL,
  VALID_SPEED,
  REGEX,
  TTS_CORE_VERSION,
  VOICE_PROVIDER,
  VALID_CHARACTERS_LENGTH_REGEX,
  SME_PACKAGE_CODES,
  VALID_CHARACTERS_REGEX,
  NON_VBEE_VOICE_PROVIDERS,
} = require('../constants');

const code = require('../errors/code');
const CustomError = require('../errors/CustomError');
const {
  getVoiceInfoByCodes,
  getVoiceInfoByCode,
  getVoiceWithCreditFactor,
} = require('./voice');
const { decryptTags } = require('./decrypt');
const {
  findAdminApp,
  getActiveWalletOfUserWithCurrentVoice,
} = require('./user');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { getPackageUsageOptions } = require('./package');
const { processWalletCreditsDeduction } = require('./processCredits');
const { SynthesisService } = require('./SynthesisService');

// ADVISE: move the core flow to CreditSevice
const countCreditsByVoiceProvider = async ({
  voiceCode,
  text,
  ssmlRegex,
  userFeatures,
  user = {},
}) => {
  const nonVbeeCreditsFactor =
    getFeatureValue(FEATURE_KEYS.NON_VBEE_CREDITS_FACTOR, {
      ...user,
      voiceCode,
    }) || 1;

  const voice = await getVoiceInfoByCode(voiceCode, user.userId, userFeatures);
  const textLength = SynthesisService.countTextLength(text, ssmlRegex);
  return NON_VBEE_VOICE_PROVIDERS.includes(voice?.provider)
    ? textLength * nonVbeeCreditsFactor
    : textLength;
};

// ADVISE: move the core flow to CreditSevice
const countCreditsBySentence = async ({
  voiceCode,
  text,
  ssmlRegex,
  userFeatures,
  user,
}) => {
  const { packageCode } = user || {};
  const voice = await getVoiceInfoByCode(voiceCode, user.userId, userFeatures);
  const voiceWithCreditFactor = getVoiceWithCreditFactor(voice, packageCode);
  const creditFactor = voiceWithCreditFactor?.creditFactor || 1;

  if (creditFactor) {
    const textLength = SynthesisService.countTextLength(text, ssmlRegex);
    return textLength * creditFactor;
  }

  // If feature CREDIT_FACTOR is not enabled, use countCreditsByVoiceProvider
  return countCreditsByVoiceProvider({
    voiceCode,
    text,
    ssmlRegex,
    userFeatures,
    user,
  });
};

// ADVISE: move the core flow to CreditSevice
const countCredits = async ({ sentences, ssmlRegex, user }) => {
  const studioUsageOptions = await getPackageUsageOptions({
    userId: user.userId,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });
  const sentenceCreditsPromises = sentences.map(async (sentence) =>
    countCreditsBySentence({
      voiceCode: sentence.voiceCode,
      text: sentence.text,
      ssmlRegex,
      userFeatures: studioUsageOptions.features,
      user,
    }),
  );

  const sentenceCredits = await Promise.all(sentenceCreditsPromises);
  const totalCredit = sentenceCredits.reduce((acc, credit) => acc + credit, 0);
  return Math.ceil(totalCredit);
};

// ADVISE: move the core flow to CreditSevice
const countCreditsUseInWallets = async ({
  voiceCode,
  text,
  ssmlRegex,
  userFeatures,
  user,
}) => {
  const { packageCode } = user || {};
  const voice = await getVoiceInfoByCode(voiceCode, user._id, userFeatures);
  const voiceWithCreditFactor = getVoiceWithCreditFactor(voice, packageCode);
  const creditFactor = voiceWithCreditFactor?.creditFactor || 1;

  const textLength = SynthesisService.countTextLength(text, ssmlRegex);

  const wallets = getActiveWalletOfUserWithCurrentVoice(user, voice);

  return { wallets, credits: textLength * creditFactor, voiceCode };
};

// ADVISE: move the core flow to CreditSevice
const countCreditsByVoiceFactor = async ({ sentences, ssmlRegex, user }) => {
  const studioUsageOptions = await getPackageUsageOptions({
    userId: user._id,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });

  const sentenceCreditsUsedFromWallets = sentences.map(async (sentence) =>
    countCreditsUseInWallets({
      voiceCode: sentence.voiceCode,
      text: sentence.text,
      ssmlRegex,
      userFeatures: studioUsageOptions.features,
      user,
    }),
  );

  const sentenceCredits = await Promise.all(sentenceCreditsUsedFromWallets);
  return sentenceCredits;
};

// ADVISE: move the core flow to CreditSevice
const isInvalidCredits = ({
  demo,
  maxLengthDemoInput,
  maxLengthInputText,
  oneTimeCredits,
  cycleCredits,
  topUpCredits,
  customCredits,
  credits,
  textLength,
}) => {
  const totalCredits =
    oneTimeCredits + cycleCredits + topUpCredits + customCredits;

  const isInputTextTooLong = demo
    ? textLength > maxLengthDemoInput
    : textLength > maxLengthInputText;

  const isCreditsExceeded = demo ? false : credits > totalCredits; // For demo, we don't check credits exceed total credits

  return isInputTextTooLong || isCreditsExceeded;
};

// ADVISE: move the core flow to CreditSevice
const validateCredits = ({
  demo,
  maxLengthDemoInput,
  maxLengthInputText,
  oneTimeCredits = 0,
  cycleCredits = 0,
  topUpCredits = 0,
  customCredits = 0,
  credits,
  textLength,
  blockedCredits,
}) => {
  const sourceCredits = {
    oneTimeCredits,
    cycleCredits,
    topUpCredits,
    customCredits,
  };

  if (blockedCredits?.cycleCredits) sourceCredits.cycleCredits = 0;
  if (blockedCredits?.topUpCredits) sourceCredits.topUpCredits = 0;
  if (blockedCredits?.customCredits) sourceCredits.customCredits = 0;
  if (blockedCredits?.oneTimeCredits) sourceCredits.oneTimeCredits = 0;

  const isExceedingLimits = isInvalidCredits({
    demo,
    maxLengthDemoInput,
    maxLengthInputText,
    credits,
    textLength,
    ...sourceCredits,
  });

  if (isExceedingLimits) {
    throw new CustomError(code.TEXT_TOO_LONG);
  }
};

// ADVISE: move the core flow to CreditSevice
const validateWalletCredits = ({ sentencesCreditsInfo, currWallets, demo }) => {
  const validWallets = processWalletCreditsDeduction({
    sentencesCreditsInfo,
    currWallets,
  });

  if (!validWallets && !demo) throw new CustomError(code.TEXT_TOO_LONG);
};

// ADVISE: using cheerio.load to test SSML validity seem to be heavy. I think we can boost the performance with more lightweight approach (regex)
const validateSsmlText = (text) => {
  const $ = cheerio.load(text, { xmlMode: true, decodeEntities: false });

  // const xml = $.xml();
  let isValidXml = true;
  // if (xml !== text) return false;

  $('emphasis').each((index, element) => {
    const level = $(element).attr('level').trim();
    if (!Object.values(EMPHASIS_LEVEL).includes(level)) {
      isValidXml = false;
      return false;
    }

    return true;
  });
  if (!isValidXml) return false;

  $('break').each((index, element) => {
    const time = $(element).attr('time').trim();
    const seconds = +time.slice(0, -1);
    const unit = time.slice(-1);

    if (unit !== 's' || seconds < 0.1 || seconds > 60) {
      isValidXml = false;
      return false;
    }

    return true;
  });
  if (!isValidXml) return false;

  $('prosody').each((index, element) => {
    const value = $(element).attr('rate').trim();
    const rate = Number(value.slice(0, -1)) / 100;

    if (rate < VALID_SPEED.MIN || rate > VALID_SPEED.MAX) {
      isValidXml = false;
      return false;
    }

    return true;
  });
  if (!isValidXml) return false;

  return isValidXml;
};

// ADVISE: what is the final purpose of this function? seem to be too complex with a lot of logic branching
const validateText = ({ text, voiceProvider, ttsCoreVersion }) => {
  let isValidText = true;

  if (voiceProvider === VOICE_PROVIDER.VBEE) {
    if (!VALID_CHARACTERS_REGEX.test(text)) return false;

    if (ttsCoreVersion !== TTS_CORE_VERSION.NEW)
      isValidText = !text.match(VALID_CHARACTERS_LENGTH_REGEX);
    else {
      // ADVISE: remove all ssml tag (matched by REGEX.ADVANCE_TAG) that we don't support. Because currently we only support <break time=1s/>
      const pureText = text.replace(REGEX.ADVANCE_TAG, '');
      isValidText = !pureText.match(VALID_CHARACTERS_LENGTH_REGEX);
    }

    if (!isValidText) return false;
  }

  if (ttsCoreVersion === TTS_CORE_VERSION.NEW) {
    isValidText = validateSsmlText(text);
  }

  return isValidText;
};

const getValidSampleRates = async (voiceCodes = [], userId, userFeatures) => {
  if (!voiceCodes.length) return [];

  const voices = await getVoiceInfoByCodes(voiceCodes, userId, userFeatures);

  const sampleRates = voices.reduce((acc, voice) => {
    const currSampleRates = voice.sampleRates || [];
    if (!acc.length) return currSampleRates;
    return currSampleRates.filter((sampleRate) => acc.indexOf(sampleRate) >= 0);
  }, []);

  return {
    sampleRates,
    maxSampleRate: sampleRates.length ? Math.max(...sampleRates) : null,
  };
};

const getTags = ({ tagsString, appId, requestId, sessionId, aesKey }) => {
  let tags = {};
  try {
    tags = JSON.parse(tagsString);
  } catch (error) {
    logger.error(error, {
      ctx: 'CachingRequest',
      appId,
      sessionId,
    });
  }

  if (aesKey) {
    tags = decryptTags({ encryptedTags: tags, aesKey, requestId, sessionId });
  }

  return tags;
};

const getTextFromTemplateAndTags = ({ template, tags }) => {
  const personalTags = template.match(REGEX.CACHING_PERSONAL_TAG);
  if (!personalTags) return template;

  const text = personalTags.reduce((acc, curr) => {
    const tag = curr.slice(1, -1);
    if (tags[tag]) acc = acc.replace(curr, tags[tag]);

    return acc;
  }, template);

  return text;
};

// ADVISE: can be moved to apiSynthesis because that where the only usage happen
/** Check for text length, package expire  */
const preCheckSynthesisApiRequest = async ({
  app,
  template,
  text,
  sessionId,
  tagsString,
  ttsCoreVersion,
  requestId,
  aesKey,
}) => {
  const textLength = SynthesisService.countSentenceTextLength(
    text,
    ttsCoreVersion,
  );
  let tags = {};

  if (tagsString)
    tags = getTags({
      tagsString,
      appId: app,
      requestId,
      sessionId,
      aesKey,
    });

  const { _id: appId } = app;

  const adminUser = await findAdminApp(app);

  const { remainingCharacters = 0, bonusCharacters = 0 } =
    adminUser.apiCharacters || {};

  const limitCredits = remainingCharacters + bonusCharacters;

  const { apiPackage } = adminUser || {};

  const { packageExpiryDate, packageCode, price } = apiPackage || {};
  // Get api usage options
  const apiUsageOptions = await getPackageUsageOptions({
    userId: adminUser._id,
    packageCode,
    userUsageOptions: adminUser.apiPackage,
  });

  const { maxLengthInputText, retentionPeriod, concurrentRequest } =
    apiUsageOptions;

  if (!packageCode) throw new CustomError(code.PACKAGE_NOT_EXIST);
  const hasExpiryPackage =
    (!packageExpiryDate && !SME_PACKAGE_CODES.includes(packageCode)) ||
    moment().isAfter(packageExpiryDate, 'day');
  if (hasExpiryPackage) throw new CustomError(code.PACKAGE_EXPIRED);

  if (!text) text = getTextFromTemplateAndTags({ template, tags });

  const hasInvalidTextLength =
    textLength > maxLengthInputText ||
    (!SME_PACKAGE_CODES.includes(packageCode) &&
      textLength > remainingCharacters + bonusCharacters);
  if (hasInvalidTextLength) throw new CustomError(code.TEXT_TOO_LONG);

  return {
    appId,
    userId: adminUser._id,
    text,
    tags,
    features: apiUsageOptions.features || [],
    price,
    packageCode,
    retentionPeriod,
    concurrentRequest,
    textLength,
    user: adminUser,
    limitCredits,
  };
};

module.exports = {
  countCredits,
  countCreditsByVoiceFactor,
  validateCredits,
  validateWalletCredits,

  validateText,
  getValidSampleRates,

  preCheckSynthesisApiRequest,
};
