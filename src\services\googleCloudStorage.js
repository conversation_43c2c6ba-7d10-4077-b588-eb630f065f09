/* eslint-disable prefer-destructuring */
require('dotenv').config();
const { Storage } = require('@google-cloud/storage');
const retry = require('retry');

const logger = require('../utils/logger');

const {
  GCS_AICORE_TTS_CLIENT_EMAIL,
  GCS_AICORE_TTS_PRIVATE_KEY,
} = require('../configs');

const storage = new Storage({
  credentials: {
    client_email: GCS_AICORE_TTS_CLIENT_EMAIL,
    private_key: GCS_AICORE_TTS_PRIVATE_KEY,
  },
});

const copyGCStoGCS = async ({ bucket, key, url }) => {
  try {
    const urlObj = new URL(url);
    const [, bucketName, ...filePathParts] = urlObj.pathname.split('/');
    const sourceBucketName = bucketName;
    const sourceFileName = filePathParts.join('/');
    const sourceBucket = storage.bucket(sourceBucketName);
    const sourceFile = sourceBucket.file(sourceFileName);

    const destinationBucketName = bucket;
    const destinationFileName = key || sourceFileName;
    const destinationBucket = storage.bucket(destinationBucketName);
    const destinationFile = destinationBucket.file(destinationFileName);
    const destinationUrl = `https://storage.googleapis.com/${destinationBucketName}/${destinationFileName}`;

    // Check if source file exists
    const [sourceFileExists] = await sourceFile.exists();
    if (!sourceFileExists) {
      logger.warn('Source file does not exist', {
        ctx: 'copyGCStoGCS',
        sourceUrl: url,
        destinationUrl,
      });
      return destinationUrl;
    }

    await sourceFile.copy(destinationFile);

    // Check if destination file exists
    const [destinationFileExists] = await destinationFile.exists();
    if (!destinationFileExists)
      logger.warn('Destination file does not exist', {
        ctx: 'copyGCStoGCS',
        sourceUrl: url,
        destinationUrl,
      });

    return destinationUrl;
  } catch (error) {
    logger.error('Error in copyGCStoGCS', {
      ctx: 'copyGCStoGCS',
      error,
      sourceUrl: url,
      destinationBucket: bucket,
      destinationKey: key,
    });
    throw error;
  }
};

const copyGCStoGCSWithRetry = async ({ bucket, key, url }) => {
  return new Promise((resolve, reject) => {
    const operation = retry.operation({
      retries: 3, // Maximum 3 retries
      factor: 2, // Exponential backoff factor
      minTimeout: 1000, // Start with 1 second delay
      maxTimeout: 10000, // Max 10 seconds between retries
      randomize: true, // Add some randomization to prevent thundering herd
    });

    operation.attempt(async (currentAttempt) => {
      try {
        const destinationUrl = await copyGCStoGCS({ bucket, key, url });
        if (!destinationUrl) throw new Error('Destination URL not found');
        resolve(destinationUrl);
      } catch (err) {
        if (operation.retry(err)) {
          logger.warn(
            `Copy GCS to GCS attempt ${currentAttempt} failed, retrying...`,
            { ctx: 'CopyGCStoGCSWithRetry', bucket, key, url },
          );
          return;
        }
        reject(operation.mainError());
      }
    });
  });
};

const getKeyFromGCSUrl = (gcsUrl) => {
  const { pathname } = new URL(gcsUrl);
  const parts = pathname.split('/');
  const key = parts.slice(2).join('/');
  return key;
};

const renameGCSFile = async ({ bucketName, oldKey, newKey }) => {
  const bucket = storage.bucket(bucketName);
  const oldFile = bucket.file(oldKey);
  const newFile = bucket.file(newKey);

  try {
    // Check if old file exists before attempting rename
    const [oldFileExists] = await oldFile.exists();
    if (!oldFileExists) {
      logger.warn('Source file does not exist', {
        ctx: 'RenameGCSFile',
        bucketName,
        oldKey,
      });
      return null;
    }

    await oldFile.copy(newFile);

    // Verify copy was successful before deleting original
    const [newFileExists] = await newFile.exists();
    if (!newFileExists) {
      logger.warn('Failed to copy file', {
        ctx: 'RenameGCSFile',
        bucketName,
        oldKey,
        newKey,
      });
      return null;
    }

    await oldFile.delete();
    const newUrl = `https://storage.googleapis.com/${bucketName}/${newKey}`;
    return newUrl;
  } catch (err) {
    logger.error(err, {
      ctx: 'RenameGCSFile',
      bucketName,
      oldKey,
      newKey,
    });
    return null;
  }
};

const checkFileExists = async ({ bucket: bucketName, key }) => {
  const bucket = storage.bucket(bucketName);
  const file = bucket.file(key);
  const [exists] = await file.exists();
  return exists;
};

const detectGCSUrl = (gcsUrl) => {
  const u = new URL(gcsUrl);
  const hostParts = u.hostname.split('.');

  let bucket;
  let key;

  // Case: https://storage.googleapis.com/my-bucket/path/to/file
  if (u.hostname === 'storage.googleapis.com') {
    const parts = u.pathname.split('/').filter(Boolean);
    bucket = parts.shift();
    key = decodeURIComponent(parts.join('/'));
  }
  // Case: https://my-bucket.storage.googleapis.com/path/to/file
  else if (
    hostParts.length >= 3 &&
    hostParts[1] === 'storage' &&
    hostParts[2] === 'googleapis'
  ) {
    bucket = hostParts[0];
    key = decodeURIComponent(u.pathname.slice(1));
  }
  // Case: console link
  else if (u.hostname === 'console.cloud.google.com') {
    const match = u.pathname.match(/storage\/browser\/([^/]+)\/(.+)/);
    if (match) {
      bucket = match[1];
      key = decodeURIComponent(match[2]);
    }
  } else {
    throw new Error('URL is not a recognized GCS format');
  }

  return { bucket, key };
};

// ADVISE: extract rename, copy to shared-lib/adapters/GCSRepository, then use it here (similar to S3Repository)
module.exports = {
  copyGCStoGCS,
  copyGCStoGCSWithRetry,
  getKeyFromGCSUrl,
  renameGCSFile,
  checkFileExists,
  detectGCSUrl,
};
