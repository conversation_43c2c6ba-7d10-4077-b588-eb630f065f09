const ttsService = require('../services/tts');

const getTts = async (req, res) => {
  const resultData = await ttsService.getTts(req.query);
  return res.send(resultData);
};

const getTtsPresets = async (req, res) => {
  const { userId } = req.user;
  const ttsPresets = await ttsService.getTtsPresets(userId);
  return res.send(ttsPresets);
};

const getTtsPresetById = async (req, res) => {
  const { userId } = req.user;
  const { ttsPresetId } = req.params;
  const ttsPreset = await ttsService.getTtsPresetById(userId, ttsPresetId);
  return res.send({ ttsPreset });
};

const createTtsPreset = async (req, res) => {
  const { userId } = req.user;
  const { name, audioType, backgroundMusic, speed, voiceCode, clientPause } =
    req.body;
  const ttsPreset = await ttsService.createTtsPreset(userId, {
    name,
    audioType,
    backgroundMusic,
    speed,
    voiceCode,
    clientPause,
  });
  return res.send({ ttsPreset });
};

const updateTtsPreset = async (req, res) => {
  const { userId } = req.user;
  const { ttsPresetId } = req.params;
  const { name, audioType, backgroundMusic, speed, voiceCode, clientPause } =
    req.body;
  const ttsPreset = await ttsService.updateTtsPreset(userId, ttsPresetId, {
    name,
    audioType,
    backgroundMusic,
    speed,
    voiceCode,
    clientPause,
  });
  return res.send({ ttsPreset });
};

const deleteTtsPreset = async (req, res) => {
  const { userId } = req.user;
  const { ttsPresetId } = req.params;
  await ttsService.deleteTtsPreset(userId, ttsPresetId);
  return res.send({});
};

const handleCallbackTTS = async (req, res) => {
  const { requestId, status, audioLink, audioType } = req.body;
  const { action } = req.query;
  await ttsService.handleCallbackTTS({
    requestId,
    status,
    audioLink,
    audioType,
    action,
  });
  return res.send({});
};

module.exports = {
  getTts,
  getTtsPresets,
  getTtsPresetById,
  createTtsPreset,
  updateTtsPreset,
  deleteTtsPreset,
  handleCallbackTTS,
};
