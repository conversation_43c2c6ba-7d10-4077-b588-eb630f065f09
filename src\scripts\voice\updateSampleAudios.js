/* eslint-disable no-plusplus */
/* eslint-disable no-continue */
require('dotenv').config();
const { initMongoDB } = require('../../models');
const voiceCloningDao = require('../../daos/voiceCloning');
const voiceCloningSampleScriptDao = require('../../daos/voiceCloningSampleScript');
const voiceCloningService = require('../../services/voiceCloning');
const logger = require('../../utils/logger');
const Caching = require('../../caching');
const { REDIS_KEY_PREFIX } = require('../../constants');
const { TTS_CALLBACK_ACTION } = require('../../constants/tts');
const { getRandomItems } = require('../../utils/array');

global.logger = logger;

(async () => {
  await initMongoDB();
  await Caching.init();

  logger.info(`Starting update voice cloning sample script...`, {
    ctx: 'RunScriptUpdateVoiceCloningSampleScript',
  });

  const communityVoiceCloning =
    await voiceCloningDao.findAllCommunityVoiceCloning();

  const communityVoiceCloningWithNoSampleAudios = communityVoiceCloning.filter(
    (voiceCloning) =>
      !voiceCloning.sampleAudios || voiceCloning.sampleAudios.length === 0,
  );

  logger.info(
    `Found ${communityVoiceCloningWithNoSampleAudios.length} voices with no sample audios`,
    {
      ctx: 'RunScriptUpdateVoiceCloningSampleScript',
    },
  );

  const voiceCloningSampleScripts =
    await voiceCloningSampleScriptDao.getVoiceCloningSampleScripts();

  const PROCESSING_QUEUE = [];
  const INITIAL_BATCH_SIZE = 10;

  for (const voiceCloning of communityVoiceCloningWithNoSampleAudios) {
    const voiceCloningSampleScript = voiceCloningSampleScripts.find(
      (script) => script.category === voiceCloning.category,
    );

    if (!voiceCloningSampleScript) {
      logger.error(
        `Voice cloning sample script not found for voice cloning ${voiceCloning.code}`,
        {
          ctx: 'RunScriptUpdateVoiceCloningSampleScript',
          voiceCloning,
        },
      );
      continue;
    }

    const selectedSampleAudios = getRandomItems(
      voiceCloningSampleScript.sampleScripts,
      10,
    );

    const updatedVoice = await voiceCloningDao.updateClonedVoiceInfo(
      voiceCloning.code,
      {
        sampleAudios: selectedSampleAudios,
      },
    );

    for (const sa of updatedVoice.sampleAudios) {
      PROCESSING_QUEUE.push({
        voiceCode: updatedVoice.code,
        sampleAudio: {
          _id: sa._id?.toString(),
          text: sa.text,
        },
      });
    }

    logger.info(`Prepared sample audios for ${voiceCloning.code}`, {
      ctx: 'RunScriptUpdateVoiceCloningSampleScript',
    });
  }

  if (PROCESSING_QUEUE.length) {
    await Caching.GlobalQueue.enqueue(
      REDIS_KEY_PREFIX.VC_SAMPLE_AUDIOS_QUEUE,
      PROCESSING_QUEUE.map((item) => JSON.stringify(item)),
    );

    logger.info(
      `Pushed ${PROCESSING_QUEUE.length} sample audios to Redis queue`,
      { ctx: 'RunScriptUpdateVoiceCloningSampleScript' },
    );

    for (let i = 0; i < INITIAL_BATCH_SIZE; i++) {
      const item = await Caching.GlobalQueue.dequeue(
        REDIS_KEY_PREFIX.VC_SAMPLE_AUDIOS_QUEUE,
      );

      if (!item) break;

      const data = JSON.parse(item);
      await voiceCloningService.requestSampleAudioTTS({
        voiceCode: data.voiceCode,
        sampleAudio: data.sampleAudio,
        action: TTS_CALLBACK_ACTION.PROCESS_NEXT_SAMPLE_AUDIO,
      });
    }
  }

  logger.info(`Script completed successfully`, {
    ctx: 'RunScriptUpdateVoiceCloningSampleScript',
  });

  process.exit(0);
})();
